using System;
using System.Collections;
using System.Collections.Generic;
using NUnit.Framework;
using UnityEngine;
using UnityEngine.TestTools;
using Cysharp.Threading.Tasks;
using Simulation.Simulators;
using Simulation.Data;

namespace Simulation.Tests
{
    /// <summary>
    /// 高级模拟器测试类 - 测试新增的模拟器功能
    /// </summary>
    public class AdvancedSimulatorTests : TestBase
    {
        #region 塔吊模拟器测试

        /// <summary>
        /// 测试塔吊模拟器状态变化
        /// </summary>
        [UnityTest]
        public IEnumerator TestCraneSimulatorStateChanges()
        {
            var craneSimulator = CreateTestComponent<CraneSimulator>();
            
            // 启动模拟器
            craneSimulator.StartSimulationAsync().Forget();
            yield return new WaitForSeconds(0.1f);
            
            Assert.IsTrue(craneSimulator.IsRunning, "塔吊模拟器应该正在运行");
            
            // 等待状态变化
            yield return new WaitForSeconds(1.0f);
            
            craneSimulator.StopSimulation();
        }

        /// <summary>
        /// 测试塔吊模拟器作业循环
        /// </summary>
        [UnityTest]
        public IEnumerator TestCraneSimulatorOperationLoop()
        {
            var craneSimulator = CreateTestComponent<CraneSimulator>();
            
            // 启动模拟器
            craneSimulator.StartSimulationAsync().Forget();
            yield return new WaitForSeconds(0.1f);
            
            // 等待作业开始
            yield return new WaitForSeconds(2.0f);
            
            craneSimulator.StopSimulation();
        }

        /// <summary>
        /// 测试塔吊模拟器安全检查
        /// </summary>
        [UnityTest]
        public IEnumerator TestCraneSimulatorSafetyChecks()
        {
            var craneSimulator = CreateTestComponent<CraneSimulator>();
            
            // 启动模拟器
            craneSimulator.StartSimulationAsync().Forget();
            yield return new WaitForSeconds(0.1f);
            
            // 等待安全检查
            yield return new WaitForSeconds(1.5f);
            
            craneSimulator.StopSimulation();
        }

        #endregion

        #region 升降机模拟器测试

        /// <summary>
        /// 测试升降机模拟器楼层移动
        /// </summary>
        [UnityTest]
        public IEnumerator TestElevatorSimulatorFloorMovement()
        {
            var elevatorSimulator = CreateTestComponent<ElevatorSimulator>();
            
            // 启动模拟器
            elevatorSimulator.StartSimulationAsync().Forget();
            yield return new WaitForSeconds(0.1f);
            
            Assert.IsTrue(elevatorSimulator.IsRunning, "升降机模拟器应该正在运行");
            
            // 等待楼层移动
            yield return new WaitForSeconds(2.0f);
            
            elevatorSimulator.StopSimulation();
        }

        /// <summary>
        /// 测试升降机模拟器载重管理
        /// </summary>
        [UnityTest]
        public IEnumerator TestElevatorSimulatorLoadManagement()
        {
            var elevatorSimulator = CreateTestComponent<ElevatorSimulator>();
            
            // 启动模拟器
            elevatorSimulator.StartSimulationAsync().Forget();
            yield return new WaitForSeconds(0.1f);
            
            // 等待载重操作
            yield return new WaitForSeconds(1.5f);
            
            elevatorSimulator.StopSimulation();
        }

        /// <summary>
        /// 测试升降机模拟器维护检查
        /// </summary>
        [UnityTest]
        public IEnumerator TestElevatorSimulatorMaintenanceCheck()
        {
            var elevatorSimulator = CreateTestComponent<ElevatorSimulator>();
            
            // 启动模拟器
            elevatorSimulator.StartSimulationAsync().Forget();
            yield return new WaitForSeconds(0.1f);
            
            // 等待维护检查
            yield return new WaitForSeconds(1.0f);
            
            elevatorSimulator.StopSimulation();
        }

        #endregion

        #region 环境监测模拟器测试

        /// <summary>
        /// 测试环境监测模拟器数据收集
        /// </summary>
        [UnityTest]
        public IEnumerator TestEnvironmentalMonitorDataCollection()
        {
            var environmentalSimulator = CreateTestComponent<EnvironmentalMonitorSimulator>();
            
            // 启动模拟器
            environmentalSimulator.StartSimulationAsync().Forget();
            yield return new WaitForSeconds(0.1f);
            
            Assert.IsTrue(environmentalSimulator.IsRunning, "环境监测模拟器应该正在运行");
            
            // 等待数据收集
            yield return new WaitForSeconds(2.0f);
            
            environmentalSimulator.StopSimulation();
        }

        /// <summary>
        /// 测试环境监测模拟器报警功能
        /// </summary>
        [UnityTest]
        public IEnumerator TestEnvironmentalMonitorAlerts()
        {
            var environmentalSimulator = CreateTestComponent<EnvironmentalMonitorSimulator>();
            
            // 启动模拟器
            environmentalSimulator.StartSimulationAsync().Forget();
            yield return new WaitForSeconds(0.1f);
            
            // 等待报警检查
            yield return new WaitForSeconds(1.5f);
            
            environmentalSimulator.StopSimulation();
        }

        /// <summary>
        /// 测试环境监测模拟器污染源检测
        /// </summary>
        [UnityTest]
        public IEnumerator TestEnvironmentalMonitorSourceDetection()
        {
            var environmentalSimulator = CreateTestComponent<EnvironmentalMonitorSimulator>();
            
            // 启动模拟器
            environmentalSimulator.StartSimulationAsync().Forget();
            yield return new WaitForSeconds(0.1f);
            
            // 等待污染源检测
            yield return new WaitForSeconds(1.0f);
            
            environmentalSimulator.StopSimulation();
        }

        #endregion

        #region 物料管理模拟器测试

        /// <summary>
        /// 测试物料管理模拟器库存管理
        /// </summary>
        [UnityTest]
        public IEnumerator TestMaterialManagementInventory()
        {
            var materialSimulator = CreateTestComponent<MaterialManagementSimulator>();
            
            // 启动模拟器
            materialSimulator.StartSimulationAsync().Forget();
            yield return new WaitForSeconds(0.1f);
            
            Assert.IsTrue(materialSimulator.IsRunning, "物料管理模拟器应该正在运行");
            
            // 等待库存操作
            yield return new WaitForSeconds(2.0f);
            
            materialSimulator.StopSimulation();
        }

        /// <summary>
        /// 测试物料管理模拟器配送流程
        /// </summary>
        [UnityTest]
        public IEnumerator TestMaterialManagementDelivery()
        {
            var materialSimulator = CreateTestComponent<MaterialManagementSimulator>();
            
            // 启动模拟器
            materialSimulator.StartSimulationAsync().Forget();
            yield return new WaitForSeconds(0.1f);
            
            // 等待配送流程
            yield return new WaitForSeconds(1.5f);
            
            materialSimulator.StopSimulation();
        }

        /// <summary>
        /// 测试物料管理模拟器质量检查
        /// </summary>
        [UnityTest]
        public IEnumerator TestMaterialManagementQualityCheck()
        {
            var materialSimulator = CreateTestComponent<MaterialManagementSimulator>();
            
            // 启动模拟器
            materialSimulator.StartSimulationAsync().Forget();
            yield return new WaitForSeconds(0.1f);
            
            // 等待质量检查
            yield return new WaitForSeconds(1.0f);
            
            materialSimulator.StopSimulation();
        }

        #endregion

        #region 天气模拟器测试

        /// <summary>
        /// 测试天气模拟器天气变化
        /// </summary>
        [UnityTest]
        public IEnumerator TestWeatherSimulatorWeatherChange()
        {
            var weatherSimulator = CreateTestComponent<WeatherSimulator>();
            
            // 启动模拟器
            weatherSimulator.StartSimulationAsync().Forget();
            yield return new WaitForSeconds(0.1f);
            
            Assert.IsTrue(weatherSimulator.IsRunning, "天气模拟器应该正在运行");
            
            // 等待天气变化
            yield return new WaitForSeconds(2.0f);
            
            weatherSimulator.StopSimulation();
        }

        /// <summary>
        /// 测试天气模拟器监测循环
        /// </summary>
        [UnityTest]
        public IEnumerator TestWeatherSimulatorMonitoring()
        {
            var weatherSimulator = CreateTestComponent<WeatherSimulator>();
            
            // 启动模拟器
            weatherSimulator.StartSimulationAsync().Forget();
            yield return new WaitForSeconds(0.1f);
            
            // 等待监测循环
            yield return new WaitForSeconds(1.5f);
            
            weatherSimulator.StopSimulation();
        }

        /// <summary>
        /// 测试天气模拟器极端天气
        /// </summary>
        [UnityTest]
        public IEnumerator TestWeatherSimulatorExtremeWeather()
        {
            var weatherSimulator = CreateTestComponent<WeatherSimulator>();
            
            // 启动模拟器
            weatherSimulator.StartSimulationAsync().Forget();
            yield return new WaitForSeconds(0.1f);
            
            // 等待极端天气检查
            yield return new WaitForSeconds(1.0f);
            
            weatherSimulator.StopSimulation();
        }

        #endregion

        #region 集成测试

        /// <summary>
        /// 测试所有新增模拟器并发运行
        /// </summary>
        [UnityTest]
        public IEnumerator TestAllNewSimulatorsConcurrency()
        {
            var craneSimulator = CreateTestComponent<CraneSimulator>();
            var elevatorSimulator = CreateTestComponent<ElevatorSimulator>();
            var environmentalSimulator = CreateTestComponent<EnvironmentalMonitorSimulator>();
            var materialSimulator = CreateTestComponent<MaterialManagementSimulator>();
            var weatherSimulator = CreateTestComponent<WeatherSimulator>();
            
            // 同时启动所有模拟器
            craneSimulator.StartSimulationAsync().Forget();
            elevatorSimulator.StartSimulationAsync().Forget();
            environmentalSimulator.StartSimulationAsync().Forget();
            materialSimulator.StartSimulationAsync().Forget();
            weatherSimulator.StartSimulationAsync().Forget();
            
            yield return new WaitForSeconds(0.2f);
            
            // 验证所有模拟器都在运行
            Assert.IsTrue(craneSimulator.IsRunning, "塔吊模拟器应该正在运行");
            Assert.IsTrue(elevatorSimulator.IsRunning, "升降机模拟器应该正在运行");
            Assert.IsTrue(environmentalSimulator.IsRunning, "环境监测模拟器应该正在运行");
            Assert.IsTrue(materialSimulator.IsRunning, "物料管理模拟器应该正在运行");
            Assert.IsTrue(weatherSimulator.IsRunning, "天气模拟器应该正在运行");
            
            // 等待一段时间让模拟器运行
            yield return new WaitForSeconds(1.0f);
            
            // 停止所有模拟器
            craneSimulator.StopSimulation();
            elevatorSimulator.StopSimulation();
            environmentalSimulator.StopSimulation();
            materialSimulator.StopSimulation();
            weatherSimulator.StopSimulation();
        }

        #endregion
    }
}
