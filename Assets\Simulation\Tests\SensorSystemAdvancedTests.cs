using System;
using System.Collections;
using System.Collections.Generic;
using System.Threading;
using NUnit.Framework;
using UnityEngine;
using UnityEngine.TestTools;
using Cysharp.Threading.Tasks;
using Simulation.Sensors;
using Simulation.Simulators;
using Simulation.Data;

namespace Simulation.Tests
{
    /// <summary>
    /// 传感器系统高级测试类
    /// 专门测试NoiseSensor和DustSensor的完整功能
    /// </summary>
    public class SensorSystemAdvancedTests : TestBase
    {
        #region 噪声传感器测试

        /// <summary>
        /// 测试噪声传感器基础功能
        /// </summary>
        [UnityTest]
        public IEnumerator TestNoiseSensorBasicFunctionality()
        {
            var noiseSensor = CreateTestComponent<NoiseSensor>();
            
            // 验证初始状态
            Assert.IsFalse(noiseSensor.IsRunning, "噪声传感器初始应该未运行");
            Assert.AreEqual("NoiseSensor", noiseSensor.SensorType, "传感器类型应该正确");
            
            // 启动传感器
            noiseSensor.StartSensor();
            yield return new WaitForSeconds(0.1f);
            
            Assert.IsTrue(noiseSensor.IsRunning, "噪声传感器应该正在运行");
            
            // 等待数据生成
            yield return new WaitForSeconds(1.2f);
            
            // 验证数据属性
            Assert.GreaterOrEqual(noiseSensor.CurrentNoiseLevel, 0f, "噪声等级应该大于等于0");
            Assert.AreEqual("dB", noiseSensor.NoiseUnit, "噪声单位应该正确");
            
            // 停止传感器
            noiseSensor.StopSensor();
            yield return new WaitForSeconds(0.1f);
            
            Assert.IsFalse(noiseSensor.IsRunning, "噪声传感器应该已停止");
        }

        /// <summary>
        /// 测试噪声传感器数据生成
        /// </summary>
        [UnityTest]
        public IEnumerator TestNoiseSensorDataGeneration()
        {
            var noiseSensor = CreateTestComponent<NoiseSensor>();
            NoiseSensorData receivedData = null;
            
            // 订阅数据事件
            noiseSensor.OnDataGenerated += (sensorId, data) =>
            {
                receivedData = data as NoiseSensorData;
            };
            
            noiseSensor.StartSensor();
            yield return new WaitForSeconds(1.5f);
            
            // 验证数据生成
            Assert.IsNotNull(receivedData, "应该生成噪声数据");
            Assert.IsNotNull(receivedData.sensorId, "传感器ID不应为空");
            Assert.GreaterOrEqual(receivedData.noiseLevel, 0f, "噪声等级应该有效");
            Assert.AreEqual("dB", receivedData.unit, "单位应该正确");
            Assert.IsTrue(receivedData.timestamp != default(DateTime), "时间戳应该有效");
            
            noiseSensor.StopSensor();
        }

        /// <summary>
        /// 测试噪声传感器阈值检测
        /// </summary>
        [UnityTest]
        public IEnumerator TestNoiseSensorThresholdDetection()
        {
            var noiseSensor = CreateTestComponent<NoiseSensor>();
            bool thresholdExceeded = false;
            
            // 设置较低的阈值以便测试
            var thresholdField = typeof(NoiseSensor).GetField("noiseThreshold", 
                System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
            thresholdField?.SetValue(noiseSensor, 40f);
            
            noiseSensor.OnDataGenerated += (sensorId, data) =>
            {
                var noiseData = data as NoiseSensorData;
                if (noiseData != null && noiseData.isThresholdExceeded)
                {
                    thresholdExceeded = true;
                }
            };
            
            noiseSensor.StartSensor();
            yield return new WaitForSeconds(3f);
            
            // 验证阈值检测功能
            Assert.IsTrue(thresholdExceeded || noiseSensor.ThresholdExceedCount >= 0, 
                "阈值检测功能应该正常工作");
            
            noiseSensor.StopSensor();
        }

        /// <summary>
        /// 测试噪声传感器与环境监测器集成
        /// </summary>
        [UnityTest]
        public IEnumerator TestNoiseSensorEnvironmentalIntegration()
        {
            var noiseSensor = CreateTestComponent<NoiseSensor>();
            var environmentalMonitor = CreateTestComponent<EnvironmentalMonitorSimulator>();
            
            // 设置环境监测器引用
            var monitorField = typeof(NoiseSensor).GetField("environmentalMonitor", 
                System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
            monitorField?.SetValue(noiseSensor, environmentalMonitor);
            
            // 启动环境监测器
            environmentalMonitor.StartSimulationAsync().Forget();
            yield return new WaitForSeconds(0.2f);
            
            // 启动噪声传感器
            noiseSensor.StartSensor();
            yield return new WaitForSeconds(1.5f);
            
            // 验证集成功能
            Assert.IsTrue(noiseSensor.IsRunning, "噪声传感器应该正在运行");
            Assert.IsTrue(environmentalMonitor.IsRunning, "环境监测器应该正在运行");
            Assert.GreaterOrEqual(noiseSensor.CurrentNoiseLevel, 0f, "应该从环境监测器获取数据");
            
            noiseSensor.StopSensor();
            environmentalMonitor.StopSimulation();
        }

        #endregion

        #region 扬尘传感器测试

        /// <summary>
        /// 测试扬尘传感器基础功能
        /// </summary>
        [UnityTest]
        public IEnumerator TestDustSensorBasicFunctionality()
        {
            var dustSensor = CreateTestComponent<DustSensor>();
            
            // 验证初始状态
            Assert.IsFalse(dustSensor.IsRunning, "扬尘传感器初始应该未运行");
            Assert.AreEqual("DustSensor", dustSensor.SensorType, "传感器类型应该正确");
            
            // 启动传感器
            dustSensor.StartSensor();
            yield return new WaitForSeconds(0.1f);
            
            Assert.IsTrue(dustSensor.IsRunning, "扬尘传感器应该正在运行");
            
            // 等待数据生成
            yield return new WaitForSeconds(1.2f);
            
            // 验证数据属性
            Assert.GreaterOrEqual(dustSensor.CurrentDustLevel, 0f, "扬尘等级应该大于等于0");
            Assert.GreaterOrEqual(dustSensor.CurrentPM25Level, 0f, "PM2.5等级应该大于等于0");
            Assert.GreaterOrEqual(dustSensor.CurrentPM10Level, 0f, "PM10等级应该大于等于0");
            Assert.AreEqual("μg/m³", dustSensor.DustUnit, "扬尘单位应该正确");
            
            // 停止传感器
            dustSensor.StopSensor();
            yield return new WaitForSeconds(0.1f);
            
            Assert.IsFalse(dustSensor.IsRunning, "扬尘传感器应该已停止");
        }

        /// <summary>
        /// 测试扬尘传感器数据生成
        /// </summary>
        [UnityTest]
        public IEnumerator TestDustSensorDataGeneration()
        {
            var dustSensor = CreateTestComponent<DustSensor>();
            DustSensorData receivedData = null;
            
            // 订阅数据事件
            dustSensor.OnDataGenerated += (sensorId, data) =>
            {
                receivedData = data as DustSensorData;
            };
            
            dustSensor.StartSensor();
            yield return new WaitForSeconds(1.5f);
            
            // 验证数据生成
            Assert.IsNotNull(receivedData, "应该生成扬尘数据");
            Assert.IsNotNull(receivedData.sensorId, "传感器ID不应为空");
            Assert.GreaterOrEqual(receivedData.totalDustLevel, 0f, "总扬尘等级应该有效");
            Assert.GreaterOrEqual(receivedData.pm25Level, 0f, "PM2.5等级应该有效");
            Assert.GreaterOrEqual(receivedData.pm10Level, 0f, "PM10等级应该有效");
            Assert.AreEqual("μg/m³", receivedData.unit, "单位应该正确");
            Assert.IsTrue(receivedData.timestamp != default(DateTime), "时间戳应该有效");
            
            dustSensor.StopSensor();
        }

        /// <summary>
        /// 测试扬尘传感器PM2.5和PM10阈值检测
        /// </summary>
        [UnityTest]
        public IEnumerator TestDustSensorThresholdDetection()
        {
            var dustSensor = CreateTestComponent<DustSensor>();
            bool pm25Exceeded = false;
            bool pm10Exceeded = false;
            
            // 设置较低的阈值以便测试
            var pm25ThresholdField = typeof(DustSensor).GetField("pm25Threshold", 
                System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
            var pm10ThresholdField = typeof(DustSensor).GetField("pm10Threshold", 
                System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
            pm25ThresholdField?.SetValue(dustSensor, 10f);
            pm10ThresholdField?.SetValue(dustSensor, 20f);
            
            dustSensor.OnDataGenerated += (sensorId, data) =>
            {
                var dustData = data as DustSensorData;
                if (dustData != null)
                {
                    if (dustData.isPM25Exceeded) pm25Exceeded = true;
                    if (dustData.isPM10Exceeded) pm10Exceeded = true;
                }
            };
            
            dustSensor.StartSensor();
            yield return new WaitForSeconds(3f);
            
            // 验证阈值检测功能
            Assert.IsTrue(pm25Exceeded || dustSensor.PM25ExceedCount >= 0, 
                "PM2.5阈值检测功能应该正常工作");
            Assert.IsTrue(pm10Exceeded || dustSensor.PM10ExceedCount >= 0, 
                "PM10阈值检测功能应该正常工作");
            
            dustSensor.StopSensor();
        }

        /// <summary>
        /// 测试扬尘传感器与环境监测器集成
        /// </summary>
        [UnityTest]
        public IEnumerator TestDustSensorEnvironmentalIntegration()
        {
            var dustSensor = CreateTestComponent<DustSensor>();
            var environmentalMonitor = CreateTestComponent<EnvironmentalMonitorSimulator>();
            
            // 设置环境监测器引用
            var monitorField = typeof(DustSensor).GetField("environmentalMonitor", 
                System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
            monitorField?.SetValue(dustSensor, environmentalMonitor);
            
            // 启动环境监测器
            environmentalMonitor.StartSimulationAsync().Forget();
            yield return new WaitForSeconds(0.2f);
            
            // 启动扬尘传感器
            dustSensor.StartSensor();
            yield return new WaitForSeconds(1.5f);
            
            // 验证集成功能
            Assert.IsTrue(dustSensor.IsRunning, "扬尘传感器应该正在运行");
            Assert.IsTrue(environmentalMonitor.IsRunning, "环境监测器应该正在运行");
            Assert.GreaterOrEqual(dustSensor.CurrentDustLevel, 0f, "应该从环境监测器获取数据");
            
            dustSensor.StopSensor();
            environmentalMonitor.StopSimulation();
        }

        #endregion

        #region 传感器配置和验证测试

        /// <summary>
        /// 测试传感器配置验证
        /// </summary>
        [Test]
        public void TestSensorConfigurationValidation()
        {
            var noiseSensor = CreateTestComponent<NoiseSensor>();
            var dustSensor = CreateTestComponent<DustSensor>();
            
            // 测试有效配置
            Assert.IsTrue(noiseSensor.ValidateConfiguration(), "噪声传感器配置应该有效");
            Assert.IsTrue(dustSensor.ValidateConfiguration(), "扬尘传感器配置应该有效");
            
            // 测试无效配置（空传感器ID）
            var sensorIdField = typeof(SensorBase).GetField("sensorId", 
                System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
            sensorIdField?.SetValue(noiseSensor, "");
            
            Assert.IsFalse(noiseSensor.ValidateConfiguration(), "空传感器ID应该导致配置无效");
        }

        /// <summary>
        /// 测试传感器采样率设置
        /// </summary>
        [Test]
        public void TestSensorSampleRateConfiguration()
        {
            var noiseSensor = CreateTestComponent<NoiseSensor>();
            
            // 测试默认采样率
            Assert.AreEqual(1.0f, noiseSensor.SampleRate, "默认采样率应该为1秒");
            
            // 测试设置新采样率
            noiseSensor.SetSampleRate(0.5f);
            Assert.AreEqual(0.5f, noiseSensor.SampleRate, "采样率应该更新为0.5秒");
            
            // 测试无效采样率
            noiseSensor.SetSampleRate(-1f);
            Assert.AreEqual(-1f, noiseSensor.SampleRate, "采样率应该被设置（验证在ValidateConfiguration中）");
        }

        #endregion
    }
}
