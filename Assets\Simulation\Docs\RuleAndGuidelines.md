# Unity建筑工地数字孪生模拟系统 - 项目规则与指导

## 📋 项目概述
基于Unity的建筑工地数字孪生模拟系统，专注生成测试数据。采用简化模块化架构，避免过度工程化。

## 🎯 核心原则
- **简单高效**：专注业务逻辑，避免复杂架构
- **异步优先**：使用UniTask替代状态机，保持逻辑高内聚
- **直接实现**：避免过度抽象，专注实际功能
- **渐进开发**：先核心功能，后续迭代

## 🏗️ 项目结构
```
Assets/Simulation/
├── Scripts/
│   ├── IoTSystem/          # IoT核心系统
│   ├── Sensors/            # 传感器系统
│   ├── Simulators/         # 业务模拟器
│   ├── Data/               # 数据结构
│   └── SimulationManager.cs
└── Tests/
    ├── TestDataTypes.cs    # 统一测试类型
    └── *Tests.cs           # 测试文件
```

**核心架构**：SimulationManager → IoTSystem → 传感器/模拟器 → MQTT通信

## 🔧 开发规范

### 1. 命名规范
- **避免命名冲突**：命名空间与类名不重复
- **统一命名空间**：使用`Simulation.IoTSystem`
- **类名**：PascalCase，方法名动词开头
- **字段**：私有camelCase，公共PascalCase

### 2. 异步编程
```csharp
// ✅ 推荐：UniTask + 超时控制
using var timeoutController = new TimeoutController();
await SomeOperation().WithCancellation(timeoutController.Timeout(TimeSpan.FromSeconds(5)));

// ✅ 模拟器模式
protected override async UniTask SimulationLoop(CancellationToken cancellationToken)
{
    while (!cancellationToken.IsCancellationRequested)
    {
        await PerformStep();
        await UniTask.Delay(TimeSpan.FromSeconds(updateInterval), cancellationToken: cancellationToken);
    }
}
```

### 3. 测试规范
- **统一测试类型**：使用`TestDataTypes.cs`，避免重复定义
- **环境隔离**：`SimulationManager.IsInTestEnvironment`区分测试/生产
- **异步测试**：使用`IEnumerator`和`UniTask`

## 📦 核心依赖
- **UniTask**：异步编程框架
- **BestMQTT**：MQTT通信插件
- **Unity Test Framework**：单元测试框架
- **包管理器优先**：使用npm/pip等工具，避免手动编辑配置文件

## 🧪 测试策略
- **测试覆盖率**：最低80%，核心组件100%
- **测试层次**：单元测试 → 集成测试 → 系统测试
- **编译验证**：每个子任务完成后必须验证编译通过

## 🔄 开发流程
1. **信息收集**：使用codebase-retrieval获取上下文
2. **分步执行**：复杂任务分解为小子任务
3. **谨慎修改**：尊重现有代码结构
4. **编译检查**：修改后立即检查编译状态
5. **测试验证**：运行相关测试确保功能正常

## ⚠️ 常见陷阱
- **命名冲突**：类名与命名空间重复
- **重复定义**：多文件定义相同类（使用TestDataTypes.cs统一）
- **过度抽象**：避免复杂继承层次
- **状态机滥用**：优先异步方法

## 🚨 故障排除
| 问题 | 解决方案 |
|------|---------|
| 编译错误 | 检查命名空间、重复定义 |
| MQTT连接失败 | 验证网络、地址、端口 |
| 传感器无数据 | 检查启动状态、采样率 |
| 测试超时 | 增加超时时间、检查异步操作 |

## 🛠️ 代码模板

### 传感器实现
```csharp
public class MySensor : SensorBase
{
    public MySensor() : base("my_sensor") { sampleRate = 1f; }
    protected override void CollectData()
    {
        var data = new MySensorData("my_sensor");
        OnDataGenerated?.Invoke(SensorId, data);
    }
}
```

### 模拟器实现
```csharp
public class MySimulator : SimulatorBase
{
    public MySimulator() : base("my_simulator") { updateInterval = 1f; }
    protected override async UniTask SimulationLoop(CancellationToken cancellationToken)
    {
        while (!cancellationToken.IsCancellationRequested)
        {
            await PerformStep();
            await UniTask.Delay(TimeSpan.FromSeconds(updateInterval), cancellationToken: cancellationToken);
        }
    }
}
```

### 测试模板
```csharp
[UnityTest]
public IEnumerator TestComponent()
{
    var component = CreateTestComponent<MyComponent>();
    bool completed = false;
    component.OnComplete += () => completed = true;
    component.Start();
    yield return WaitForCondition(() => completed, 5f, "超时");
    Assert.IsTrue(completed);
}
```

### Inspector配置
```csharp
[Header("基础配置")]
[SerializeField] private float updateRate = 1f;
[SerializeField] private bool autoStart = true;

[Header("调试")]
[SerializeField] private bool enableDebugLog = false;

[ContextMenu("启动")]
public void StartFromMenu() => StartAsync().Forget();
```

## 📖 快速上手
1. **阅读文档**：本文档 → 需求文档 → 开发指导
2. **环境搭建**：Unity 2022.3 + UniTask + BestMQTT
3. **代码熟悉**：SimulationManager → IoTSystem → 各组件
4. **开发实践**：遵循异步模式，使用统一测试类型

---
**核心理念**：简单高效，专注业务，渐进迭代
