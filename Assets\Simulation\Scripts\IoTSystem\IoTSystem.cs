using System;
using System.Collections.Generic;
using UnityEngine;
using Cysharp.Threading.Tasks;
using System.Threading;
using Simulation.Sensors;

namespace Simulation.IoT
{
    /// <summary>
    /// IoT系统单例类，负责传感器管理、数据收集和MQTT发布
    /// </summary>
    public class IoTSystem : MonoBehaviour
    {
        [Header("MQTT连接配置")]
        [SerializeField] private string brokerAddress = "localhost";
        [SerializeField] private int brokerPort = 1883;
        [SerializeField] private string clientId = "simulation_system";
        [SerializeField] private string username = "";
        [SerializeField] private string password = "";
        [SerializeField] private bool useSSL = false;
        [SerializeField] private int keepAliveInterval = 60;
        [SerializeField] private int reconnectDelay = 5;

        [Header("系统配置")]
        [SerializeField] private bool autoStart = true;
        [SerializeField] private bool enableDebugLog = true;
        [SerializeField] private float dataPublishInterval = 1.0f;
        [SerializeField] private int maxQueueSize = 1000;

        // 单例实例
        public static IoTSystem Instance { get; private set; }

        // 传感器管理
        private readonly Dictionary<string, SensorBase> registeredSensors = new Dictionary<string, SensorBase>();
        private readonly Queue<SensorDataPacket> dataQueue = new Queue<SensorDataPacket>();
        private readonly Queue<EventPacket> eventQueue = new Queue<EventPacket>();

        // 系统状态
        private bool isRunning = false;
        private bool isConnected = false;
        private CancellationTokenSource cancellationTokenSource;

        // MQTT管理器
        private MQTTManager mqttManager;

        // 事件管理器
        private EventManager eventManager;

        // 公共属性
        public bool IsRunning => isRunning;
        public bool IsConnected => isConnected;
        public int RegisteredSensorCount => registeredSensors.Count;
        public int QueuedDataCount => dataQueue.Count;
        public int QueuedEventCount => eventQueue.Count;

        // 事件
        public event Action OnSystemStarted;
        public event Action OnSystemStopped;
        public event Action<bool> OnConnectionChanged;
        public event Action<string> OnSystemError;

        private void Awake()
        {
            // 单例模式实现
            if (Instance == null)
            {
                Instance = this;
                DontDestroyOnLoad(gameObject);
                LogDebug("IoT系统单例已创建");
            }
            else
            {
                LogWarning("IoT系统单例已存在，销毁重复实例");
                Destroy(gameObject);
            }
        }

        private void Start()
        {
            if (autoStart)
            {
                StartSystemAsync().Forget();
            }
        }

        private void OnDestroy()
        {
            StopSystem();
        }

        /// <summary>
        /// 启动IoT系统
        /// </summary>
        public async UniTaskVoid StartSystemAsync()
        {
            if (isRunning)
            {
                LogDebug("IoT系统已在运行中");
                return;
            }

            try
            {
                cancellationTokenSource = new CancellationTokenSource();
                isRunning = true;

                LogDebug("IoT系统启动中...");

                // 初始化MQTT连接
                await InitializeMQTTAsync(cancellationTokenSource.Token);

                // 初始化事件管理器
                InitializeEventManager();

                // 启动数据处理循环
                DataProcessingLoop(cancellationTokenSource.Token).Forget();

                OnSystemStarted?.Invoke();
                LogDebug("IoT系统启动完成");
            }
            catch (Exception ex)
            {
                LogError($"IoT系统启动失败: {ex.Message}");
                OnSystemError?.Invoke(ex.Message);
                isRunning = false;
            }
        }

        /// <summary>
        /// 停止IoT系统
        /// </summary>
        public void StopSystem()
        {
            if (!isRunning) return;

            try
            {
                LogDebug("IoT系统停止中...");

                cancellationTokenSource?.Cancel();
                isRunning = false;
                isConnected = false;

                // 清理资源
                registeredSensors.Clear();
                dataQueue.Clear();
                eventQueue.Clear();

                OnSystemStopped?.Invoke();
                OnConnectionChanged?.Invoke(false);
                LogDebug("IoT系统已停止");
            }
            catch (Exception ex)
            {
                LogError($"IoT系统停止异常: {ex.Message}");
            }
        }

        /// <summary>
        /// 注册传感器
        /// </summary>
        public bool RegisterSensor(SensorBase sensor)
        {
            if (sensor == null || string.IsNullOrEmpty(sensor.SensorId))
            {
                LogError("尝试注册空传感器");
                return false;
            }

            if (registeredSensors.ContainsKey(sensor.SensorId))
            {
                LogWarning($"传感器已注册: {sensor.SensorId}");
                return false;
            }

            registeredSensors[sensor.SensorId] = sensor;
            sensor.OnDataGenerated += OnSensorDataGenerated;
            LogDebug($"传感器注册成功: {sensor.SensorId} ({sensor.SensorType})");
            return true;
        }

        /// <summary>
        /// 注销传感器
        /// </summary>
        public bool UnregisterSensor(string sensorId)
        {
            if (string.IsNullOrEmpty(sensorId))
            {
                LogError("尝试注销空传感器");
                return false;
            }
            
            if (registeredSensors.TryGetValue(sensorId, out var sensor))
            {
                sensor.OnDataGenerated -= OnSensorDataGenerated;
                registeredSensors.Remove(sensorId);
                LogDebug($"传感器注销成功: {sensorId}");
                return true;
            }

            LogWarning($"尝试注销不存在的传感器: {sensorId}");
            return false;
        }

        /// <summary>
        /// 收集传感器数据
        /// </summary>
        public void CollectSensorData(string sensorId, object data)
        {
            if (!isRunning) return;

            var packet = new SensorDataPacket
            {
                SensorId = sensorId,
                Timestamp = DateTime.UtcNow,
                Data = data
            };

            lock (dataQueue)
            {
                if (dataQueue.Count >= maxQueueSize)
                {
                    dataQueue.Dequeue(); // 移除最旧的数据
                    LogWarning("数据队列已满，移除最旧数据");
                }
                dataQueue.Enqueue(packet);
            }
        }

        /// <summary>
        /// 发送IoT事件
        /// </summary>
        public void SendIOTEvent(string sourceId, string eventType, string title, string description, object data = null)
        {
            if (!isRunning) return;

            var eventPacket = new EventPacket
            {
                EventId = Guid.NewGuid().ToString(),
                EventType = eventType,
                SourceId = sourceId,
                Timestamp = DateTime.UtcNow,
                Title = title,
                Description = description,
                Data = data
            };

            // 使用事件管理器处理事件
            bool shouldProcess = true;
            if (eventManager != null)
            {
                shouldProcess = eventManager.ProcessEvent(eventPacket);
            }

            // 只有通过事件管理器处理的事件才加入队列
            if (shouldProcess)
            {
                lock (eventQueue)
                {
                    if (eventQueue.Count >= maxQueueSize)
                    {
                        eventQueue.Dequeue(); // 移除最旧的事件
                        LogWarning("事件队列已满，移除最旧事件");
                    }
                    eventQueue.Enqueue(eventPacket);
                }

                LogDebug($"IoT事件已加入队列: {eventType} - {title}");
            }
            else
            {
                LogDebug($"IoT事件被过滤: {eventType} - {title}");
            }
        }

        /// <summary>
        /// 传感器数据生成事件处理
        /// </summary>
        private void OnSensorDataGenerated(string sensorId, object data)
        {
            CollectSensorData(sensorId, data);
        }

        /// <summary>
        /// 检查传感器是否已注册
        /// </summary>
        public bool IsSensorRegistered(string sensorId)
        {
            return !string.IsNullOrEmpty(sensorId) && registeredSensors.ContainsKey(sensorId);
        }

        /// <summary>
        /// 获取已注册传感器列表
        /// </summary>
        public List<string> GetRegisteredSensors()
        {
            return new List<string>(registeredSensors.Keys);
        }

        /// <summary>
        /// 初始化MQTT连接
        /// </summary>
        private async UniTask InitializeMQTTAsync(CancellationToken cancellationToken)
        {
            // 获取或创建MQTT管理器
            mqttManager = GetComponent<MQTTManager>();
            if (mqttManager == null)
            {
                mqttManager = gameObject.AddComponent<MQTTManager>();
            }

            // 订阅MQTT事件
            mqttManager.OnConnected += () => {
                isConnected = true;
                OnConnectionChanged?.Invoke(true);
                LogDebug($"MQTT连接已建立: {brokerAddress}:{brokerPort}");
            };

            mqttManager.OnDisconnected += () => {
                isConnected = false;
                OnConnectionChanged?.Invoke(false);
                LogDebug("MQTT连接已断开");
            };

            mqttManager.OnConnectionError += (error) => {
                LogError($"MQTT连接错误: {error}");
                OnSystemError?.Invoke(error);
            };

            // 尝试连接
            bool connected = await mqttManager.ConnectAsync();
            if (!connected)
            {
                throw new Exception("MQTT连接失败");
            }
        }

        /// <summary>
        /// 初始化事件管理器
        /// </summary>
        private void InitializeEventManager()
        {
            // 获取或创建事件管理器
            eventManager = GetComponent<EventManager>();
            if (eventManager == null)
            {
                eventManager = gameObject.AddComponent<EventManager>();
            }

            LogDebug("事件管理器初始化完成");
        }

        /// <summary>
        /// 数据处理循环
        /// </summary>
        private async UniTaskVoid DataProcessingLoop(CancellationToken cancellationToken)
        {
            try
            {
                while (!cancellationToken.IsCancellationRequested && isRunning)
                {
                    await ProcessQueuedData(cancellationToken);
                    await ProcessQueuedEvents(cancellationToken);
                    await UniTask.Delay(TimeSpan.FromSeconds(dataPublishInterval), cancellationToken: cancellationToken);
                }
            }
            catch (OperationCanceledException)
            {
                LogDebug("数据处理循环被取消");
            }
            catch (Exception ex)
            {
                LogError($"数据处理循环异常: {ex.Message}");
                OnSystemError?.Invoke(ex.Message);
            }
        }

        /// <summary>
        /// 处理队列中的传感器数据
        /// </summary>
        private async UniTask ProcessQueuedData(CancellationToken cancellationToken)
        {
            var dataToProcess = new List<SensorDataPacket>();

            lock (dataQueue)
            {
                while (dataQueue.Count > 0)
                {
                    dataToProcess.Add(dataQueue.Dequeue());
                }
            }

            foreach (var packet in dataToProcess)
            {
                await PublishSensorDataAsync(packet, cancellationToken);
            }
        }

        /// <summary>
        /// 处理队列中的事件
        /// </summary>
        private async UniTask ProcessQueuedEvents(CancellationToken cancellationToken)
        {
            var eventsToProcess = new List<EventPacket>();

            lock (eventQueue)
            {
                while (eventQueue.Count > 0)
                {
                    eventsToProcess.Add(eventQueue.Dequeue());
                }
            }

            foreach (var eventPacket in eventsToProcess)
            {
                await PublishEventAsync(eventPacket, cancellationToken);
            }
        }

        /// <summary>
        /// 发布传感器数据到MQTT
        /// </summary>
        private async UniTask PublishSensorDataAsync(SensorDataPacket packet, CancellationToken cancellationToken)
        {
            if (mqttManager == null || !mqttManager.IsConnected)
            {
                LogWarning("MQTT未连接，无法发布传感器数据");
                return;
            }

            try
            {
                string topic = $"sensors/{packet.SensorId}/data";
                bool success = mqttManager.PublishMessage(topic, packet, false);

                if (success)
                {
                    LogDebug($"发布传感器数据: {packet.SensorId}");
                }
                else
                {
                    LogError($"发布传感器数据失败: {packet.SensorId}");
                }
            }
            catch (Exception ex)
            {
                LogError($"发布传感器数据异常: {ex.Message}");
            }

            await UniTask.Yield(cancellationToken);
        }

        /// <summary>
        /// 发布事件到MQTT
        /// </summary>
        private async UniTask PublishEventAsync(EventPacket eventPacket, CancellationToken cancellationToken)
        {
            if (mqttManager == null || !mqttManager.IsConnected)
            {
                LogWarning("MQTT未连接，无法发布事件");
                return;
            }

            try
            {
                string topic = $"events/{eventPacket.EventType}";
                bool success = mqttManager.PublishMessage(topic, eventPacket, false);

                if (success)
                {
                    LogDebug($"发布事件: {eventPacket.EventType} - {eventPacket.Title}");
                }
                else
                {
                    LogError($"发布事件失败: {eventPacket.EventType}");
                }
            }
            catch (Exception ex)
            {
                LogError($"发布事件异常: {ex.Message}");
            }

            await UniTask.Yield(cancellationToken);
        }

        /// <summary>
        /// 调试日志
        /// </summary>
        private void LogDebug(string message)
        {
            if (enableDebugLog)
            {
                Debug.Log($"[IoTSystem] {message}");
            }
        }

        /// <summary>
        /// 警告日志
        /// </summary>
        private void LogWarning(string message)
        {
            Debug.LogWarning($"[IoTSystem] {message}");
        }

        /// <summary>
        /// 错误日志
        /// </summary>
        private void LogError(string message)
        {
            //如果是在UnityTestFramework测试环境下则不报错
            if (SimulationManager.IsInTestEnvironment)
            {
                Debug.Log($"<color=red>TestError: {message}</color>");
                return;
            }
                
            Debug.LogError($"[IoTSystem] {message}");
        }
    }

    /// <summary>
    /// 传感器数据包
    /// </summary>
    [Serializable]
    public class SensorDataPacket
    {
        public string SensorId;
        public DateTime Timestamp;
        public object Data;
    }

    /// <summary>
    /// 事件数据包
    /// </summary>
    [Serializable]
    public class EventPacket
    {
        public string EventId;
        public string EventType;
        public string SourceId;
        public DateTime Timestamp;
        public string Title;
        public string Description;
        public object Data;
    }
}
