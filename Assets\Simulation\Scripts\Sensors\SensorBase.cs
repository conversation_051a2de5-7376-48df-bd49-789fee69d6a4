using System;
using System.Threading;
using Cysharp.Threading.Tasks;
using UnityEngine;
using Simulation.IoT;

namespace Simulation.Sensors
{
    /// <summary>
    /// 传感器基类
    /// 定义了所有传感器的通用行为和接口
    /// </summary>
    public abstract class SensorBase : MonoBehaviour
    {
        [Header("传感器基础配置")]
        [SerializeField] protected string sensorId;
        [SerializeField] protected string deviceId;
        [SerializeField] protected float sampleRate = 1.0f; // 采样频率（秒）
        [SerializeField] protected bool autoStart = true;
        [SerializeField] protected bool enableDebugLog = false;
        
        // 传感器状态
        protected bool isRunning = false;
        protected CancellationTokenSource cancellationTokenSource;
        
        // 公共属性
        public string SensorId => sensorId;
        public string DeviceId => deviceId;
        public float SampleRate => sampleRate;
        public bool IsRunning => isRunning;
        public abstract string SensorType { get; }
        
        // 事件
        public event Action<string, object> OnDataGenerated;
        public event Action<string> OnSensorError;
        
        protected virtual void Awake()
        {
            // 如果没有设置ID，自动生成
            if (string.IsNullOrEmpty(sensorId))
            {
                sensorId = $"{SensorType}_{GetInstanceID()}";
            }
            
            if (string.IsNullOrEmpty(deviceId))
            {
                deviceId = $"device_{GetInstanceID()}";
            }
        }

        protected virtual void Start()
        {
            // 只在非测试环境下自动注册
            if (!SimulationManager.IsInTestEnvironment)
            {
                IoTSystem.Instance?.RegisterSensor(this);
            }

            if (autoStart)
            {
                StartSensor();
            }
        }
        
        protected virtual void OnDestroy()
        {
            StopSensor();
        }

        /// <summary>
        /// 获取传感器ID
        /// </summary>
        public string GetSensorId()
        {
            return sensorId;
        }

        /// <summary>
        /// 获取设备ID
        /// </summary>
        public string GetDeviceId()
        {
            return deviceId;
        }

        /// <summary>
        /// 获取传感器类型
        /// </summary>
        public string GetSensorType()
        {
            return SensorType;
        }

        /// <summary>
        /// 启动传感器
        /// </summary>
        public virtual void StartSensor()
        {
            if (isRunning)
            {
                LogDebug("传感器已在运行中");
                return;
            }
            
            cancellationTokenSource = new CancellationTokenSource();
            isRunning = true;
            
            LogDebug("传感器启动");
            OnSensorStarted();
            
            // 开始数据采集循环
            SensorDataLoop(cancellationTokenSource.Token).Forget();
        }
        
        /// <summary>
        /// 停止传感器
        /// </summary>
        public virtual void StopSensor()
        {
            if (!isRunning) return;
            
            try
            {
                LogDebug("传感器停止");
                
                cancellationTokenSource?.Cancel();
                isRunning = false;
                
                // 从IoT系统注销
                IoTSystem.Instance?.UnregisterSensor(sensorId);
                
                OnSensorStopped();
            }
            catch (Exception ex)
            {
                LogError($"传感器停止异常: {ex.Message}");
            }
        }
        
        /// <summary>
        /// 传感器数据采集循环
        /// </summary>
        protected async UniTaskVoid SensorDataLoop(CancellationToken cancellationToken)
        {
            try
            {
                while (!cancellationToken.IsCancellationRequested && isRunning)
                {
                    // 生成传感器数据
                    var data = await GenerateSensorDataAsync(cancellationToken);
                    
                    if (data != null)
                    {
                        // 触发数据生成事件
                        OnDataGenerated?.Invoke(sensorId, data);
                        LogDebug($"生成数据: {data}");
                    }
                    
                    // 等待下一次采样
                    await UniTask.Delay(TimeSpan.FromSeconds(sampleRate), cancellationToken: cancellationToken);
                }
            }
            catch (OperationCanceledException)
            {
                LogDebug("传感器数据循环被取消");
            }
            catch (Exception ex)
            {
                LogError($"传感器数据循环异常: {ex.Message}");
                OnSensorError?.Invoke(ex.Message);
            }
        }
        
        /// <summary>
        /// 生成传感器数据（抽象方法，由子类实现）
        /// </summary>
        protected abstract UniTask<object> GenerateSensorDataAsync(CancellationToken cancellationToken);
        
        /// <summary>
        /// 传感器启动时调用
        /// </summary>
        protected virtual void OnSensorStarted()
        {
            // 子类可以重写此方法
        }
        
        /// <summary>
        /// 传感器停止时调用
        /// </summary>
        protected virtual void OnSensorStopped()
        {
            // 子类可以重写此方法
        }
        
        /// <summary>
        /// 验证传感器配置
        /// </summary>
        public virtual bool ValidateConfiguration()
        {
            if (string.IsNullOrEmpty(sensorId))
            {
                LogError("传感器ID不能为空");
                return false;
            }
            
            if (string.IsNullOrEmpty(deviceId))
            {
                LogError("设备ID不能为空");
                return false;
            }
            
            if (sampleRate <= 0)
            {
                LogError("采样频率必须大于0");
                return false;
            }
            
            return true;
        }

        /// <summary>
        /// 设置采样率
        /// </summary>
        /// <param name="newSampleRate"></param>
        public virtual void SetSampleRate(float newSampleRate)
        {
            sampleRate = newSampleRate;
            LogDebug($"采样率已更新: {sampleRate}");
        }
        
        /// <summary>
        /// 手动上报数据到IoT系统
        /// </summary>
        protected virtual void ReportData(object data)
        {
            IoTSystem.Instance?.CollectSensorData(sensorId, data);
        }

        /// <summary>
        /// 触发数据生成事件（供子类调用）
        /// </summary>
        protected virtual void TriggerDataGenerated(object data)
        {
            OnDataGenerated?.Invoke(sensorId, data);
        }
        
        /// <summary>
        /// 调试日志
        /// </summary>
        protected void LogDebug(string message)
        {
            if (enableDebugLog)
            {
                Debug.Log($"[{SensorType}:{sensorId}] {message}");
            }
        }
        
        /// <summary>
        /// 错误日志
        /// </summary>
        protected void LogError(string message)
        {
            Debug.LogError($"[{SensorType}:{sensorId}] {message}");
        }
        
        /// <summary>
        /// 警告日志
        /// </summary>
        protected void LogWarning(string message)
        {
            Debug.LogWarning($"[{SensorType}:{sensorId}] {message}");
        }
    }
}
