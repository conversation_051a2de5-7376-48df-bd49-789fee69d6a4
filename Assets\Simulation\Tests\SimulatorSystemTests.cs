using System.Collections;
using System.Threading;
using UnityEngine;
using UnityEngine.TestTools;
using NUnit.Framework;
using Cysharp.Threading.Tasks;
using Simulation.Simulators;
using Simulation.IoT;

namespace Simulation.Tests
{
    /// <summary>
    /// 模拟器系统单元测试
    /// </summary>
    public class SimulatorSystemTests : TestBase
    {
        private TestSimulator testSimulator;
        
        [SetUp]
        public override void SetUp()
        {
            base.SetUp();
            
            // 创建测试模拟器
            testSimulator = CreateTestComponent<TestSimulator>();
        }
        
        /// <summary>
        /// 测试模拟器启动和停止
        /// </summary>
        [UnityTest]
        public IEnumerator TestSimulatorStartStop()
        {
            Assert.IsFalse(testSimulator.IsRunning(), "模拟器初始状态应该是停止的");
            
            // 启动模拟器
            testSimulator.StartSimulationAsync().Forget();
            yield return new WaitForSeconds(0.1f);
            
            Assert.IsTrue(testSimulator.IsRunning(), "模拟器应该正在运行");
            
            // 停止模拟器
            testSimulator.StopSimulation();
            yield return new WaitForSeconds(0.1f);
            
            Assert.IsFalse(testSimulator.IsRunning(), "模拟器应该已停止");
        }
        
        /// <summary>
        /// 测试模拟器循环更新
        /// </summary>
        [UnityTest]
        public IEnumerator TestSimulatorUpdateLoop()
        {
            int initialUpdateCount = testSimulator.GetUpdateCount();
            
            // 启动模拟器
            testSimulator.StartSimulationAsync().Forget();
            
            // 等待几帧更新
            yield return new WaitForSeconds(0.2f);
            
            int afterRunningCount = testSimulator.GetUpdateCount();
            Assert.Greater(afterRunningCount, initialUpdateCount, "模拟器应该执行了更新循环");
            
            // 停止模拟器
            testSimulator.StopSimulation();
            yield return new WaitForSeconds(0.1f);
            
            int afterStopCount = testSimulator.GetUpdateCount();
            
            // 再等待一段时间，确认更新已停止
            yield return new WaitForSeconds(0.2f);
            
            int finalCount = testSimulator.GetUpdateCount();
            Assert.AreEqual(afterStopCount, finalCount, "停止后模拟器不应该继续更新");
        }
        
        /// <summary>
        /// 测试工人模拟器基础功能
        /// </summary>
        [UnityTest]
        public IEnumerator TestWorkerSimulatorBasics()
        {
            var workerSimulator = CreateTestComponent<WorkerSimulator>();

            // 启动模拟器
            workerSimulator.StartSimulationAsync().Forget();
            yield return new WaitForSeconds(0.1f);

            Assert.IsTrue(workerSimulator.IsRunning, "工人模拟器应该正在运行");

            workerSimulator.StopSimulation();
            yield return new WaitForSeconds(0.1f);

            Assert.IsFalse(workerSimulator.IsRunning, "工人模拟器应该已停止");
        }
        
        /// <summary>
        /// 测试车辆模拟器基础功能
        /// </summary>
        [UnityTest]
        public IEnumerator TestVehicleSimulatorBasics()
        {
            var vehicleSimulator = CreateTestComponent<VehicleSimulator>();

            // 启动模拟器
            vehicleSimulator.StartSimulationAsync().Forget();
            yield return new WaitForSeconds(0.1f);

            Assert.IsTrue(vehicleSimulator.IsRunning, "车辆模拟器应该正在运行");

            vehicleSimulator.StopSimulation();
            yield return new WaitForSeconds(0.1f);

            Assert.IsFalse(vehicleSimulator.IsRunning, "车辆模拟器应该已停止");
        }

        /// <summary>
        /// 测试摄像头模拟器基础功能
        /// </summary>
        [UnityTest]
        public IEnumerator TestCameraSimulatorBasics()
        {
            var cameraSimulator = CreateTestComponent<CameraMonitorSimulator>();

            // 启动模拟器
            cameraSimulator.StartSimulationAsync().Forget();
            yield return new WaitForSeconds(0.1f);
            Assert.IsTrue(cameraSimulator.IsRunning, "摄像头模拟器应该正在运行");

            cameraSimulator.StopSimulation();
            yield return new WaitForSeconds(0.1f);

            Assert.IsFalse(cameraSimulator.IsRunning, "摄像头模拟器应该已停止");
        }
        
        /// <summary>
        /// 测试模拟器事件发送
        /// </summary>
        [UnityTest]
        public IEnumerator TestSimulatorEventSending()
        {
            bool eventReceived = false;
            string receivedEventType = null;
            
            // 模拟事件接收（这里简化处理，实际应该通过IoT系统）
            testSimulator.OnEventSent += (eventType, data) =>
            {
                eventReceived = true;
                receivedEventType = eventType;
            };
            
            // 启动模拟器
            testSimulator.StartSimulationAsync();
            
            // 触发事件发送
            testSimulator.TriggerTestEvent();
            
            yield return new WaitForSeconds(0.1f);
            
            Assert.IsTrue(eventReceived, "应该接收到事件");
            Assert.AreEqual("test_event", receivedEventType, "事件类型应该匹配");
            
            testSimulator.StopSimulation();
        }
        
        /// <summary>
        /// 测试多个模拟器并发运行
        /// </summary>
        [UnityTest]
        public IEnumerator TestMultipleSimulatorsConcurrency()
        {
            var simulator1 = CreateTestComponent<TestSimulator>();
            var simulator2 = CreateTestComponent<TestSimulator>();
            var simulator3 = CreateTestComponent<TestSimulator>();
            
            // 同时启动多个模拟器
            simulator1.StartSimulationAsync().Forget();
            simulator2.StartSimulationAsync().Forget();
            simulator3.StartSimulationAsync().Forget();
            
            yield return new WaitForSeconds(0.2f);
            
            // 验证所有模拟器都在运行
            Assert.IsTrue(simulator1.IsRunning(), "模拟器1应该正在运行");
            Assert.IsTrue(simulator2.IsRunning(), "模拟器2应该正在运行");
            Assert.IsTrue(simulator3.IsRunning(), "模拟器3应该正在运行");
            
            // 验证所有模拟器都在更新
            Assert.Greater(simulator1.GetUpdateCount(), 0, "模拟器1应该有更新");
            Assert.Greater(simulator2.GetUpdateCount(), 0, "模拟器2应该有更新");
            Assert.Greater(simulator3.GetUpdateCount(), 0, "模拟器3应该有更新");
            
            // 停止所有模拟器
            simulator1.StopSimulation();
            simulator2.StopSimulation();
            simulator3.StopSimulation();
        }
    }
    
    /// <summary>
    /// 测试用模拟器类
    /// </summary>
    public class TestSimulator : SimulatorBase
    {
        private bool isRunning = false;
        private int updateCount = 0;
        private bool shouldThrowException = false;
        
        public System.Action<string, object> OnEventSent;
        
        public bool IsRunning() => isRunning;
        public int GetUpdateCount() => updateCount;
        
        public void SetShouldThrowException(bool shouldThrow)
        {
            shouldThrowException = shouldThrow;
        }
        
        public void TriggerTestEvent()
        {
            OnEventSent?.Invoke("test_event", new { message = "测试事件" });
        }
        
        protected override async UniTask OnStartSimulationAsync(CancellationToken cancellationToken)
        {
            isRunning = true;
            await UniTask.Yield(PlayerLoopTiming.Update, cancellationToken);
        }
        
        protected override void OnSimulationUpdate(float deltaTime)
        {
            updateCount++;
            
            if (shouldThrowException && updateCount == 5)
            {
                // 模拟异常情况
                throw new System.Exception("测试异常");
            }
        }
        
        protected override async UniTask OnStopSimulationAsync()
        {
            isRunning = false;
            await UniTask.Yield();
        }
    }
}
