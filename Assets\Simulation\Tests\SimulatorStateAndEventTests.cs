using System;
using System.Collections;
using System.Collections.Generic;
using NUnit.Framework;
using UnityEngine;
using UnityEngine.TestTools;
using Cysharp.Threading.Tasks;
using Simulation.Simulators;
using Simulation.Data;
using Simulation.IoT;

namespace Simulation.Tests
{
    /// <summary>
    /// 模拟器状态和事件测试类
    /// </summary>
    public class SimulatorStateAndEventTests : TestBase
    {
        private List<IoTEventData> receivedEvents;

        [SetUp]
        public override void SetUp()
        {
            base.SetUp();
            receivedEvents = new List<IoTEventData>();
        }

        #region 塔吊模拟器状态测试

        /// <summary>
        /// 测试塔吊模拟器状态枚举
        /// </summary>
        [Test]
        public void TestCraneStateEnum()
        {
            // 验证塔吊状态枚举值
            Assert.AreEqual(0, (int)CraneState.Idle);
            Assert.AreEqual(1, (int)CraneState.MovingToLift);
            Assert.AreEqual(2, (int)CraneState.Lifting);
            Assert.AreEqual(3, (int)CraneState.MovingToPlace);
            Assert.AreEqual(4, (int)CraneState.Placing);
            Assert.AreEqual(5, (int)CraneState.Returning);
            Assert.AreEqual(6, (int)CraneState.Emergency);
            Assert.AreEqual(7, (int)CraneState.Maintenance);
        }

        /// <summary>
        /// 测试塔吊模拟器事件发送
        /// </summary>
        [UnityTest]
        public IEnumerator TestCraneSimulatorEvents()
        {
            var craneSimulator = CreateTestComponent<CraneSimulator>();
            
            // 启动模拟器
            craneSimulator.StartSimulationAsync().Forget();
            yield return new WaitForSeconds(0.1f);
            
            // 等待事件发送
            yield return new WaitForSeconds(1.0f);
            
            craneSimulator.StopSimulation();
        }

        /// <summary>
        /// 测试塔吊模拟器安全事件
        /// </summary>
        [UnityTest]
        public IEnumerator TestCraneSimulatorSafetyEvents()
        {
            var craneSimulator = CreateTestComponent<CraneSimulator>();
            
            // 启动模拟器
            craneSimulator.StartSimulationAsync().Forget();
            yield return new WaitForSeconds(0.1f);
            
            // 等待安全事件
            yield return new WaitForSeconds(2.0f);
            
            craneSimulator.StopSimulation();
        }

        #endregion

        #region 升降机模拟器状态测试

        /// <summary>
        /// 测试升降机模拟器状态枚举
        /// </summary>
        [Test]
        public void TestElevatorStateEnum()
        {
            // 验证升降机状态枚举值
            Assert.AreEqual(0, (int)ElevatorState.Idle);
            Assert.AreEqual(1, (int)ElevatorState.MovingUp);
            Assert.AreEqual(2, (int)ElevatorState.MovingDown);
            Assert.AreEqual(3, (int)ElevatorState.OpeningDoor);
            Assert.AreEqual(4, (int)ElevatorState.ClosingDoor);
            Assert.AreEqual(5, (int)ElevatorState.Loading);
            Assert.AreEqual(6, (int)ElevatorState.Unloading);
            Assert.AreEqual(7, (int)ElevatorState.Emergency);
            Assert.AreEqual(8, (int)ElevatorState.Maintenance);
        }

        /// <summary>
        /// 测试升降机模拟器事件发送
        /// </summary>
        [UnityTest]
        public IEnumerator TestElevatorSimulatorEvents()
        {
            var elevatorSimulator = CreateTestComponent<ElevatorSimulator>();
            
            // 启动模拟器
            elevatorSimulator.StartSimulationAsync().Forget();
            yield return new WaitForSeconds(0.1f);
            
            // 等待事件发送
            yield return new WaitForSeconds(1.0f);
            
            elevatorSimulator.StopSimulation();
        }

        /// <summary>
        /// 测试升降机模拟器载重事件
        /// </summary>
        [UnityTest]
        public IEnumerator TestElevatorSimulatorLoadEvents()
        {
            var elevatorSimulator = CreateTestComponent<ElevatorSimulator>();
            
            // 启动模拟器
            elevatorSimulator.StartSimulationAsync().Forget();
            yield return new WaitForSeconds(0.1f);
            
            // 等待载重事件
            yield return new WaitForSeconds(2.0f);
            
            elevatorSimulator.StopSimulation();
        }

        #endregion

        #region 环境监测模拟器状态测试

        /// <summary>
        /// 测试环境监测模拟器状态枚举
        /// </summary>
        [Test]
        public void TestMonitoringStateEnum()
        {
            // 验证监测状态枚举值
            Assert.AreEqual(0, (int)MonitoringState.Monitoring);
            Assert.AreEqual(1, (int)MonitoringState.Calibrating);
            Assert.AreEqual(2, (int)MonitoringState.Maintenance);
            Assert.AreEqual(3, (int)MonitoringState.Error);
        }

        /// <summary>
        /// 测试环境监测模拟器数据事件
        /// </summary>
        [UnityTest]
        public IEnumerator TestEnvironmentalMonitorDataEvents()
        {
            var environmentalSimulator = CreateTestComponent<EnvironmentalMonitorSimulator>();
            
            // 启动模拟器
            environmentalSimulator.StartSimulationAsync().Forget();
            yield return new WaitForSeconds(0.1f);
            
            // 等待数据事件
            yield return new WaitForSeconds(1.5f);
            
            environmentalSimulator.StopSimulation();
        }

        /// <summary>
        /// 测试环境监测模拟器报警事件
        /// </summary>
        [UnityTest]
        public IEnumerator TestEnvironmentalMonitorAlertEvents()
        {
            var environmentalSimulator = CreateTestComponent<EnvironmentalMonitorSimulator>();
            
            // 启动模拟器
            environmentalSimulator.StartSimulationAsync().Forget();
            yield return new WaitForSeconds(0.1f);
            
            // 等待报警事件
            yield return new WaitForSeconds(2.0f);
            
            environmentalSimulator.StopSimulation();
        }

        #endregion

        #region 物料管理模拟器测试

        /// <summary>
        /// 测试物料管理模拟器属性访问
        /// </summary>
        [UnityTest]
        public IEnumerator TestMaterialManagementProperties()
        {
            var materialSimulator = CreateTestComponent<MaterialManagementSimulator>();
            
            // 启动模拟器
            materialSimulator.StartSimulationAsync().Forget();
            yield return new WaitForSeconds(0.1f);
            
            // 验证属性访问
            Assert.IsNotNull(materialSimulator.WarehouseId, "仓库ID不应为空");
            Assert.GreaterOrEqual(materialSimulator.CurrentCapacityUsed, 0f, "当前容量使用应大于等于0");
            Assert.GreaterOrEqual(materialSimulator.CapacityUtilization, 0f, "容量利用率应大于等于0");
            Assert.GreaterOrEqual(materialSimulator.MaterialTypeCount, 0, "物料类型数量应大于等于0");
            Assert.GreaterOrEqual(materialSimulator.TotalDeliveries, 0, "总配送次数应大于等于0");
            Assert.GreaterOrEqual(materialSimulator.TotalConsumptions, 0, "总消耗次数应大于等于0");
            Assert.GreaterOrEqual(materialSimulator.QualityIssues, 0, "质量问题次数应大于等于0");
            
            materialSimulator.StopSimulation();
        }

        /// <summary>
        /// 测试物料管理模拟器配送事件
        /// </summary>
        [UnityTest]
        public IEnumerator TestMaterialManagementDeliveryEvents()
        {
            var materialSimulator = CreateTestComponent<MaterialManagementSimulator>();
            
            // 启动模拟器
            materialSimulator.StartSimulationAsync().Forget();
            yield return new WaitForSeconds(0.1f);
            
            // 等待配送事件
            yield return new WaitForSeconds(2.0f);
            
            materialSimulator.StopSimulation();
        }

        /// <summary>
        /// 测试物料管理模拟器质量事件
        /// </summary>
        [UnityTest]
        public IEnumerator TestMaterialManagementQualityEvents()
        {
            var materialSimulator = CreateTestComponent<MaterialManagementSimulator>();
            
            // 启动模拟器
            materialSimulator.StartSimulationAsync().Forget();
            yield return new WaitForSeconds(0.1f);
            
            // 等待质量事件
            yield return new WaitForSeconds(1.5f);
            
            materialSimulator.StopSimulation();
        }

        #endregion

        #region 天气模拟器状态测试

        /// <summary>
        /// 测试天气模拟器天气类型枚举
        /// </summary>
        [Test]
        public void TestWeatherTypeEnum()
        {
            // 验证天气类型枚举值
            Assert.AreEqual(0, (int)WeatherType.Sunny);
            Assert.AreEqual(1, (int)WeatherType.Cloudy);
            Assert.AreEqual(2, (int)WeatherType.Rainy);
            Assert.AreEqual(3, (int)WeatherType.Stormy);
            Assert.AreEqual(4, (int)WeatherType.Foggy);
            Assert.AreEqual(5, (int)WeatherType.Snowy);
        }

        /// <summary>
        /// 测试天气模拟器数据事件
        /// </summary>
        [UnityTest]
        public IEnumerator TestWeatherSimulatorDataEvents()
        {
            var weatherSimulator = CreateTestComponent<WeatherSimulator>();
            
            // 启动模拟器
            weatherSimulator.StartSimulationAsync().Forget();
            yield return new WaitForSeconds(0.1f);
            
            // 等待数据事件
            yield return new WaitForSeconds(1.5f);
            
            weatherSimulator.StopSimulation();
        }

        /// <summary>
        /// 测试天气模拟器变化事件
        /// </summary>
        [UnityTest]
        public IEnumerator TestWeatherSimulatorChangeEvents()
        {
            var weatherSimulator = CreateTestComponent<WeatherSimulator>();
            
            // 启动模拟器
            weatherSimulator.StartSimulationAsync().Forget();
            yield return new WaitForSeconds(0.1f);
            
            // 等待天气变化事件
            yield return new WaitForSeconds(2.0f);
            
            weatherSimulator.StopSimulation();
        }

        /// <summary>
        /// 测试天气模拟器极端天气事件
        /// </summary>
        [UnityTest]
        public IEnumerator TestWeatherSimulatorExtremeEvents()
        {
            var weatherSimulator = CreateTestComponent<WeatherSimulator>();
            
            // 启动模拟器
            weatherSimulator.StartSimulationAsync().Forget();
            yield return new WaitForSeconds(0.1f);
            
            // 等待极端天气事件
            yield return new WaitForSeconds(1.0f);
            
            weatherSimulator.StopSimulation();
        }

        #endregion

        #region 事件验证辅助方法

        /// <summary>
        /// 验证事件是否包含指定类型
        /// </summary>
        private bool ContainsEventType(string eventType)
        {
            foreach (var eventData in receivedEvents)
            {
                if (eventData.eventType == eventType)
                {
                    return true;
                }
            }
            return false;
        }

        /// <summary>
        /// 获取指定类型的事件数量
        /// </summary>
        private int GetEventCount(string eventType)
        {
            int count = 0;
            foreach (var eventData in receivedEvents)
            {
                if (eventData.eventType == eventType)
                {
                    count++;
                }
            }
            return count;
        }

        #endregion
    }
}
