using System;
using System.Collections;
using System.Collections.Generic;
using NUnit.Framework;
using UnityEngine;
using UnityEngine.TestTools;
using Simulation.Simulators;
using Simulation.Data;

namespace Simulation.Tests
{
    /// <summary>
    /// 模拟器数据结构测试类
    /// </summary>
    public class SimulatorDataStructureTests : TestBase
    {
        #region 塔吊数据结构测试

        /// <summary>
        /// 测试塔吊作业数据结构
        /// </summary>
        [Test]
        public void TestCraneOperationDataStructure()
        {
            var operation = new CraneOperation
            {
                operationId = Guid.NewGuid().ToString(),
                liftPoint = null,
                placePoint = null,
                loadWeight = 1500.5f,
                startTime = DateTime.UtcNow
            };

            Assert.IsNotNull(operation.operationId, "作业ID不应为空");
            Assert.IsNotEmpty(operation.operationId, "作业ID不应为空字符串");
            Assert.AreEqual(1500.5f, operation.loadWeight, 0.01f, "载重应该匹配");
            Assert.IsTrue(operation.startTime <= DateTime.UtcNow, "开始时间应该在当前时间之前或等于");
        }

        /// <summary>
        /// 测试塔吊作业数据序列化
        /// </summary>
        [Test]
        public void TestCraneOperationSerialization()
        {
            var operation = new CraneOperation
            {
                operationId = "test_operation_001",
                loadWeight = 2000f,
                startTime = new DateTime(2024, 1, 1, 10, 0, 0)
            };

            // 测试JSON序列化
            string json = JsonUtility.ToJson(operation);
            Assert.IsNotNull(json, "JSON序列化结果不应为空");
            Assert.IsTrue(json.Contains("test_operation_001"), "JSON应包含作业ID");
            Assert.IsTrue(json.Contains("2000"), "JSON应包含载重信息");

            // 测试反序列化
            var deserializedOperation = JsonUtility.FromJson<CraneOperation>(json);
            Assert.AreEqual(operation.operationId, deserializedOperation.operationId, "反序列化后作业ID应匹配");
            Assert.AreEqual(operation.loadWeight, deserializedOperation.loadWeight, 0.01f, "反序列化后载重应匹配");
        }

        #endregion

        #region 升降机数据结构测试

        /// <summary>
        /// 测试升降机乘客数据结构
        /// </summary>
        [Test]
        public void TestElevatorPassengerDataStructure()
        {
            var passenger = new ElevatorPassenger
            {
                passengerId = Guid.NewGuid().ToString(),
                passengerCount = 3,
                materialWeight = 150.5f,
                fromFloor = 1,
                toFloor = 5
            };

            Assert.IsNotNull(passenger.passengerId, "乘客ID不应为空");
            Assert.IsNotEmpty(passenger.passengerId, "乘客ID不应为空字符串");
            Assert.AreEqual(3, passenger.passengerCount, "乘客数量应该匹配");
            Assert.AreEqual(150.5f, passenger.materialWeight, 0.01f, "物料重量应该匹配");
            Assert.AreEqual(1, passenger.fromFloor, "起始楼层应该匹配");
            Assert.AreEqual(5, passenger.toFloor, "目标楼层应该匹配");
        }

        /// <summary>
        /// 测试升降机请求数据结构
        /// </summary>
        [Test]
        public void TestElevatorRequestDataStructure()
        {
            var request = new ElevatorRequest
            {
                requestId = "req_001",
                fromFloor = 0,
                toFloor = 3,
                passengerCount = 2,
                materialWeight = 100f,
                requestTime = DateTime.UtcNow
            };

            Assert.AreEqual("req_001", request.requestId, "请求ID应该匹配");
            Assert.AreEqual(0, request.fromFloor, "起始楼层应该匹配");
            Assert.AreEqual(3, request.toFloor, "目标楼层应该匹配");
            Assert.AreEqual(2, request.passengerCount, "乘客数量应该匹配");
            Assert.AreEqual(100f, request.materialWeight, 0.01f, "物料重量应该匹配");
        }

        #endregion

        #region 环境监测数据结构测试

        /// <summary>
        /// 测试环境数据结构
        /// </summary>
        [Test]
        public void TestEnvironmentalDataStructure()
        {
            var data = new EnvironmentalData
            {
                timestamp = DateTime.UtcNow,
                noiseLevel = 65.5f,
                dustLevel = 120.3f,
                stationId = "station_001",
                location = new Vector3(10f, 0f, 20f)
            };

            Assert.IsTrue(data.timestamp <= DateTime.UtcNow, "时间戳应该在当前时间之前或等于");
            Assert.AreEqual(65.5f, data.noiseLevel, 0.01f, "噪声水平应该匹配");
            Assert.AreEqual(120.3f, data.dustLevel, 0.01f, "扬尘水平应该匹配");
            Assert.AreEqual("station_001", data.stationId, "监测站ID应该匹配");
            Assert.AreEqual(new Vector3(10f, 0f, 20f), data.location, "位置应该匹配");
        }

        /// <summary>
        /// 测试环境数据序列化
        /// </summary>
        [Test]
        public void TestEnvironmentalDataSerialization()
        {
            var data = new EnvironmentalData
            {
                timestamp = new DateTime(2024, 1, 1, 12, 0, 0),
                noiseLevel = 70f,
                dustLevel = 150f,
                stationId = "test_station",
                location = Vector3.zero
            };

            // 测试JSON序列化
            string json = JsonUtility.ToJson(data);
            Assert.IsNotNull(json, "JSON序列化结果不应为空");
            Assert.IsTrue(json.Contains("test_station"), "JSON应包含监测站ID");
            Assert.IsTrue(json.Contains("70"), "JSON应包含噪声水平");

            // 测试反序列化
            var deserializedData = JsonUtility.FromJson<EnvironmentalData>(json);
            Assert.AreEqual(data.stationId, deserializedData.stationId, "反序列化后监测站ID应匹配");
            Assert.AreEqual(data.noiseLevel, deserializedData.noiseLevel, 0.01f, "反序列化后噪声水平应匹配");
            Assert.AreEqual(data.dustLevel, deserializedData.dustLevel, 0.01f, "反序列化后扬尘水平应匹配");
        }

        #endregion

        #region 物料管理数据结构测试

        /// <summary>
        /// 测试物料类型配置数据结构
        /// </summary>
        [Test]
        public void TestMaterialTypeConfigDataStructure()
        {
            var config = new MaterialTypeConfig
            {
                materialId = "mat_001",
                materialName = "水泥",
                unit = "吨",
                density = 1.5f,
                unitPrice = 350f,
                shelfLife = 90,
                storageTemperature = 20f,
                isHazardous = false
            };

            Assert.AreEqual("mat_001", config.materialId, "物料ID应该匹配");
            Assert.AreEqual("水泥", config.materialName, "物料名称应该匹配");
            Assert.AreEqual("吨", config.unit, "单位应该匹配");
            Assert.AreEqual(1.5f, config.density, 0.01f, "密度应该匹配");
            Assert.AreEqual(350f, config.unitPrice, 0.01f, "单价应该匹配");
            Assert.AreEqual(90, config.shelfLife, "保质期应该匹配");
            Assert.AreEqual(20f, config.storageTemperature, 0.01f, "存储温度应该匹配");
            Assert.IsFalse(config.isHazardous, "危险品标识应该匹配");
        }

        /// <summary>
        /// 测试物料库存数据结构
        /// </summary>
        [Test]
        public void TestMaterialInventoryDataStructure()
        {
            var inventory = new MaterialInventory
            {
                materialId = "mat_002",
                materialName = "钢筋",
                currentStock = 500f,
                maxCapacity = 1000f,
                unit = "吨",
                unitPrice = 4500f,
                lastUpdated = DateTime.UtcNow,
                batches = new List<MaterialBatch>()
            };

            Assert.AreEqual("mat_002", inventory.materialId, "物料ID应该匹配");
            Assert.AreEqual("钢筋", inventory.materialName, "物料名称应该匹配");
            Assert.AreEqual(500f, inventory.currentStock, 0.01f, "当前库存应该匹配");
            Assert.AreEqual(1000f, inventory.maxCapacity, 0.01f, "最大容量应该匹配");
            Assert.AreEqual("吨", inventory.unit, "单位应该匹配");
            Assert.AreEqual(4500f, inventory.unitPrice, 0.01f, "单价应该匹配");
            Assert.IsNotNull(inventory.batches, "批次列表不应为空");
            Assert.AreEqual(0, inventory.batches.Count, "初始批次列表应为空");
        }

        /// <summary>
        /// 测试物料批次数据结构
        /// </summary>
        [Test]
        public void TestMaterialBatchDataStructure()
        {
            var batch = new MaterialBatch
            {
                batchId = "batch_001",
                quantity = 100f,
                receivedDate = DateTime.UtcNow,
                expirationDate = DateTime.UtcNow.AddDays(30),
                quality = 0.95f,
                supplierId = "supplier_001",
                isDefective = false
            };

            Assert.AreEqual("batch_001", batch.batchId, "批次ID应该匹配");
            Assert.AreEqual(100f, batch.quantity, 0.01f, "数量应该匹配");
            Assert.IsTrue(batch.receivedDate <= DateTime.UtcNow, "接收日期应该在当前时间之前或等于");
            Assert.IsTrue(batch.expirationDate > DateTime.UtcNow, "过期日期应该在当前时间之后");
            Assert.AreEqual(0.95f, batch.quality, 0.01f, "质量应该匹配");
            Assert.AreEqual("supplier_001", batch.supplierId, "供应商ID应该匹配");
            Assert.IsFalse(batch.isDefective, "缺陷标识应该匹配");
        }

        #endregion

        #region 数据验证测试

        /// <summary>
        /// 测试数据结构边界值
        /// </summary>
        [Test]
        public void TestDataStructureBoundaryValues()
        {
            // 测试负值处理
            var operation = new CraneOperation
            {
                loadWeight = -100f
            };
            Assert.AreEqual(-100f, operation.loadWeight, 0.01f, "应该能处理负载重值");

            // 测试零值处理
            var passenger = new ElevatorPassenger
            {
                passengerCount = 0,
                materialWeight = 0f
            };
            Assert.AreEqual(0, passenger.passengerCount, "应该能处理零乘客数量");
            Assert.AreEqual(0f, passenger.materialWeight, 0.01f, "应该能处理零物料重量");

            // 测试极大值处理
            var data = new EnvironmentalData
            {
                noiseLevel = float.MaxValue,
                dustLevel = float.MaxValue
            };
            Assert.AreEqual(float.MaxValue, data.noiseLevel, "应该能处理最大噪声值");
            Assert.AreEqual(float.MaxValue, data.dustLevel, "应该能处理最大扬尘值");
        }

        /// <summary>
        /// 测试数据结构空值处理
        /// </summary>
        [Test]
        public void TestDataStructureNullValues()
        {
            // 测试空字符串
            var config = new MaterialTypeConfig
            {
                materialId = null,
                materialName = "",
                unit = null
            };
            Assert.IsNull(config.materialId, "应该能处理空物料ID");
            Assert.AreEqual("", config.materialName, "应该能处理空物料名称");
            Assert.IsNull(config.unit, "应该能处理空单位");

            // 测试空列表
            var inventory = new MaterialInventory
            {
                batches = null
            };
            Assert.IsNull(inventory.batches, "应该能处理空批次列表");
        }

        #endregion
    }
}
