using System;
using System.Collections;
using System.Threading;
using NUnit.Framework;
using UnityEngine;
using UnityEngine.TestTools;
using Cysharp.Threading.Tasks;
using Simulation.Sensors;
using Simulation.Simulators;

namespace Simulation.Tests
{
    /// <summary>
    /// 传感器错误处理和边界情况测试类
    /// 测试传感器在异常情况下的行为
    /// </summary>
    public class SensorErrorHandlingTests : TestBase
    {
        #region 传感器基础错误处理测试

        /// <summary>
        /// 测试传感器重复启动处理
        /// </summary>
        [UnityTest]
        public IEnumerator TestSensorDoubleStartHandling()
        {
            var noiseSensor = CreateTestComponent<NoiseSensor>();
            
            // 第一次启动
            noiseSensor.StartSensor();
            yield return new WaitForSeconds(0.1f);
            Assert.IsTrue(noiseSensor.IsRunning, "传感器应该正在运行");
            
            // 第二次启动（应该被忽略）
            noiseSensor.StartSensor();
            yield return new WaitForSeconds(0.1f);
            Assert.IsTrue(noiseSensor.IsRunning, "传感器应该仍在运行");
            
            noiseSensor.StopSensor();
        }

        /// <summary>
        /// 测试传感器重复停止处理
        /// </summary>
        [UnityTest]
        public IEnumerator TestSensorDoubleStopHandling()
        {
            var dustSensor = CreateTestComponent<DustSensor>();
            
            // 启动传感器
            dustSensor.StartSensor();
            yield return new WaitForSeconds(0.1f);
            Assert.IsTrue(dustSensor.IsRunning, "传感器应该正在运行");
            
            // 第一次停止
            dustSensor.StopSensor();
            yield return new WaitForSeconds(0.1f);
            Assert.IsFalse(dustSensor.IsRunning, "传感器应该已停止");
            
            // 第二次停止（应该被安全处理）
            Assert.DoesNotThrow(() => dustSensor.StopSensor(), "重复停止不应抛出异常");
            Assert.IsFalse(dustSensor.IsRunning, "传感器应该仍为停止状态");
        }

        /// <summary>
        /// 测试传感器配置验证错误处理
        /// </summary>
        [Test]
        public void TestSensorConfigurationValidationErrors()
        {
            var noiseSensor = CreateTestComponent<NoiseSensor>();
            
            // 测试空传感器ID
            var sensorIdField = typeof(SensorBase).GetField("sensorId", 
                System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
            sensorIdField?.SetValue(noiseSensor, "");
            
            Assert.IsFalse(noiseSensor.ValidateConfiguration(), "空传感器ID应该导致验证失败");
            
            // 测试空设备ID
            sensorIdField?.SetValue(noiseSensor, "valid_sensor_id");
            var deviceIdField = typeof(SensorBase).GetField("deviceId", 
                System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
            deviceIdField?.SetValue(noiseSensor, "");
            
            Assert.IsFalse(noiseSensor.ValidateConfiguration(), "空设备ID应该导致验证失败");
            
            // 测试无效采样率
            deviceIdField?.SetValue(noiseSensor, "valid_device_id");
            var sampleRateField = typeof(SensorBase).GetField("sampleRate", 
                System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
            sampleRateField?.SetValue(noiseSensor, -1f);
            
            Assert.IsFalse(noiseSensor.ValidateConfiguration(), "负采样率应该导致验证失败");
        }

        #endregion

        #region 环境监测器集成错误处理测试

        /// <summary>
        /// 测试缺失环境监测器的处理
        /// </summary>
        [UnityTest]
        public IEnumerator TestMissingEnvironmentalMonitorHandling()
        {
            var noiseSensor = CreateTestComponent<NoiseSensor>();
            
            // 确保没有环境监测器引用
            var monitorField = typeof(NoiseSensor).GetField("environmentalMonitor", 
                System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
            monitorField?.SetValue(noiseSensor, null);
            
            // 禁用自动查找
            var autoFindField = typeof(NoiseSensor).GetField("autoFindMonitor", 
                System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
            autoFindField?.SetValue(noiseSensor, false);
            
            // 启动传感器（应该使用模拟数据）
            noiseSensor.StartSensor();
            yield return new WaitForSeconds(1.5f);
            
            // 验证传感器仍能正常工作
            Assert.IsTrue(noiseSensor.IsRunning, "传感器应该正在运行");
            Assert.GreaterOrEqual(noiseSensor.CurrentNoiseLevel, 0f, "应该生成模拟数据");
            
            noiseSensor.StopSensor();
        }

        /// <summary>
        /// 测试环境监测器停用时的处理
        /// </summary>
        [UnityTest]
        public IEnumerator TestDisabledEnvironmentalMonitorHandling()
        {
            var dustSensor = CreateTestComponent<DustSensor>();
            var environmentalMonitor = CreateTestComponent<EnvironmentalMonitorSimulator>();
            
            // 设置环境监测器引用
            var monitorField = typeof(DustSensor).GetField("environmentalMonitor", 
                System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
            monitorField?.SetValue(dustSensor, environmentalMonitor);
            
            // 禁用环境监测器
            environmentalMonitor.enabled = false;
            
            // 启动扬尘传感器
            dustSensor.StartSensor();
            yield return new WaitForSeconds(1.5f);
            
            // 验证传感器使用模拟数据
            Assert.IsTrue(dustSensor.IsRunning, "传感器应该正在运行");
            Assert.GreaterOrEqual(dustSensor.CurrentDustLevel, 0f, "应该生成模拟数据");
            
            dustSensor.StopSensor();
        }

        #endregion

        #region 数据生成异常处理测试

        /// <summary>
        /// 测试传感器数据生成异常处理
        /// </summary>
        [UnityTest]
        public IEnumerator TestSensorDataGenerationExceptionHandling()
        {
            var noiseSensor = CreateTestComponent<NoiseSensor>();
            bool errorEventTriggered = false;
            string errorMessage = "";
            
            // 订阅错误事件
            noiseSensor.OnSensorError += (message) =>
            {
                errorEventTriggered = true;
                errorMessage = message;
            };
            
            // 设置极端的采样率来可能触发异常
            noiseSensor.SetSampleRate(0.001f);
            
            noiseSensor.StartSensor();
            yield return new WaitForSeconds(2f);
            
            // 验证传感器能处理高频采样
            Assert.IsTrue(noiseSensor.IsRunning, "传感器应该仍在运行");
            
            noiseSensor.StopSensor();
        }

        /// <summary>
        /// 测试传感器取消令牌处理
        /// </summary>
        [UnityTest]
        public IEnumerator TestSensorCancellationTokenHandling()
        {
            var dustSensor = CreateTestComponent<DustSensor>();
            
            // 启动传感器
            dustSensor.StartSensor();
            yield return new WaitForSeconds(0.5f);
            Assert.IsTrue(dustSensor.IsRunning, "传感器应该正在运行");
            
            // 立即停止传感器（测试取消令牌）
            dustSensor.StopSensor();
            yield return new WaitForSeconds(0.1f);
            
            Assert.IsFalse(dustSensor.IsRunning, "传感器应该已停止");
        }

        #endregion

        #region 边界值和极端情况测试

        /// <summary>
        /// 测试传感器极端采样率处理
        /// </summary>
        [Test]
        public void TestExtremeSampleRates()
        {
            var noiseSensor = CreateTestComponent<NoiseSensor>();
            
            // 测试极小采样率
            noiseSensor.SetSampleRate(0.001f);
            Assert.AreEqual(0.001f, noiseSensor.SampleRate, "极小采样率应该被设置");
            
            // 测试极大采样率
            noiseSensor.SetSampleRate(3600f);
            Assert.AreEqual(3600f, noiseSensor.SampleRate, "极大采样率应该被设置");
            
            // 测试零采样率
            noiseSensor.SetSampleRate(0f);
            Assert.AreEqual(0f, noiseSensor.SampleRate, "零采样率应该被设置");
            Assert.IsFalse(noiseSensor.ValidateConfiguration(), "零采样率应该导致配置无效");
        }

        /// <summary>
        /// 测试传感器阈值边界情况
        /// </summary>
        [UnityTest]
        public IEnumerator TestSensorThresholdBoundaryConditions()
        {
            var dustSensor = CreateTestComponent<DustSensor>();
            bool thresholdEventTriggered = false;
            
            // 设置阈值为0（边界情况）
            var pm25ThresholdField = typeof(DustSensor).GetField("pm25Threshold", 
                System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
            pm25ThresholdField?.SetValue(dustSensor, 0f);
            
            dustSensor.OnDataGenerated += (sensorId, data) =>
            {
                var dustData = data as DustSensorData;
                if (dustData != null && dustData.isPM25Exceeded)
                {
                    thresholdEventTriggered = true;
                }
            };
            
            dustSensor.StartSensor();
            yield return new WaitForSeconds(2f);
            
            // 验证零阈值处理
            Assert.IsTrue(thresholdEventTriggered || dustSensor.PM25ExceedCount >= 0, 
                "零阈值应该被正确处理");
            
            dustSensor.StopSensor();
        }

        /// <summary>
        /// 测试传感器GameObject销毁时的处理
        /// </summary>
        [UnityTest]
        public IEnumerator TestSensorGameObjectDestroyHandling()
        {
            var sensorGameObject = new GameObject("TestSensor");
            var noiseSensor = sensorGameObject.AddComponent<NoiseSensor>();
            
            // 启动传感器
            noiseSensor.StartSensor();
            yield return new WaitForSeconds(0.5f);
            Assert.IsTrue(noiseSensor.IsRunning, "传感器应该正在运行");
            
            // 销毁GameObject
            UnityEngine.Object.DestroyImmediate(sensorGameObject);
            yield return new WaitForSeconds(0.1f);
            
            // 验证没有异常抛出（传感器应该在OnDestroy中清理）
            Assert.IsTrue(true, "GameObject销毁应该被安全处理");
        }

        #endregion

        #region 内存和性能测试

        /// <summary>
        /// 测试传感器内存泄漏预防
        /// </summary>
        [UnityTest]
        public IEnumerator TestSensorMemoryLeakPrevention()
        {
            var noiseSensor = CreateTestComponent<NoiseSensor>();
            
            // 多次启动停止传感器
            for (int i = 0; i < 5; i++)
            {
                noiseSensor.StartSensor();
                yield return new WaitForSeconds(0.2f);
                noiseSensor.StopSensor();
                yield return new WaitForSeconds(0.1f);
            }
            
            // 验证最终状态
            Assert.IsFalse(noiseSensor.IsRunning, "传感器应该处于停止状态");
            
            // 强制垃圾回收
            System.GC.Collect();
            yield return new WaitForSeconds(0.1f);
            
            Assert.IsTrue(true, "内存泄漏测试完成");
        }

        /// <summary>
        /// 测试多传感器并发错误处理
        /// </summary>
        [UnityTest]
        public IEnumerator TestMultipleSensorsConcurrentErrorHandling()
        {
            var noiseSensor1 = CreateTestComponent<NoiseSensor>("NoiseSensor1");
            var noiseSensor2 = CreateTestComponent<NoiseSensor>("NoiseSensor2");
            var dustSensor1 = CreateTestComponent<DustSensor>("DustSensor1");
            var dustSensor2 = CreateTestComponent<DustSensor>("DustSensor2");
            
            // 同时启动所有传感器
            noiseSensor1.StartSensor();
            noiseSensor2.StartSensor();
            dustSensor1.StartSensor();
            dustSensor2.StartSensor();
            
            yield return new WaitForSeconds(1f);
            
            // 验证所有传感器都在运行
            Assert.IsTrue(noiseSensor1.IsRunning, "噪声传感器1应该正在运行");
            Assert.IsTrue(noiseSensor2.IsRunning, "噪声传感器2应该正在运行");
            Assert.IsTrue(dustSensor1.IsRunning, "扬尘传感器1应该正在运行");
            Assert.IsTrue(dustSensor2.IsRunning, "扬尘传感器2应该正在运行");
            
            // 同时停止所有传感器
            noiseSensor1.StopSensor();
            noiseSensor2.StopSensor();
            dustSensor1.StopSensor();
            dustSensor2.StopSensor();
            
            yield return new WaitForSeconds(0.2f);
            
            // 验证所有传感器都已停止
            Assert.IsFalse(noiseSensor1.IsRunning, "噪声传感器1应该已停止");
            Assert.IsFalse(noiseSensor2.IsRunning, "噪声传感器2应该已停止");
            Assert.IsFalse(dustSensor1.IsRunning, "扬尘传感器1应该已停止");
            Assert.IsFalse(dustSensor2.IsRunning, "扬尘传感器2应该已停止");
        }

        #endregion
    }
}
