using System.Collections;
using System.Threading;
using UnityEngine;
using UnityEngine.TestTools;
using NUnit.Framework;
using Cysharp.Threading.Tasks;
using Simulation.Sensors;
using Simulation.Data;
using Simulation.IoT;

namespace Simulation.Tests
{
    /// <summary>
    /// 传感器系统单元测试
    /// </summary>
    public class SensorSystemTests : TestBase
    {
        private TestSensor testSensor;
        private Transform targetTransform;
        
        [SetUp]
        public override void SetUp()
        {
            base.SetUp();
            
            // 创建测试传感器
            testSensor = CreateTestComponent<TestSensor>();
            
            // 创建目标Transform
            targetTransform = CreateTestTransform(new Vector3(1, 2, 3), Quaternion.Euler(45, 90, 0));
        }
        
        /// <summary>
        /// 测试传感器基础配置
        /// </summary>
        [Test]
        public void TestSensorBasicConfiguration()
        {
            // 配置传感器
            testSensor.SetSampleRate(2.0f);
            Assert.AreEqual(2.0f, testSensor.GetDataCollectionInterval(), "数据收集间隔应该匹配");
        }
        
        /// <summary>
        /// 测试传感器启动和停止
        /// </summary>
        [UnityTest]
        public IEnumerator TestSensorStartStop()
        {
            // 启动传感器
            testSensor.StartSensor();
            yield return new WaitForSeconds(0.05f);
            
            Assert.IsTrue(testSensor.IsRunning, "传感器应该正在收集数据");
            
            // 停止传感器
            testSensor.StopSensor();
            yield return new WaitForSeconds(0.05f);
            
            Assert.IsFalse(testSensor.IsRunning, "传感器应该已停止收集数据");
        }
        
        /// <summary>
        /// 测试数据收集间隔
        /// </summary>
        [UnityTest]
        public IEnumerator TestDataCollectionInterval()
        {
            float interval = 0.2f;
            testSensor.SetSampleRate(interval);
            
            int initialDataCount = testSensor.GetDataCollectionCount();
            
            // 启动数据收集
            testSensor.StartSensor();
            
            // 等待一个间隔周期
            yield return new WaitForSeconds(interval + 0.1f);
            
            int afterOneInterval = testSensor.GetDataCollectionCount();
            Assert.Greater(afterOneInterval, initialDataCount, "应该收集到新数据");
            
            // 等待另一个间隔周期
            yield return new WaitForSeconds(interval);
            
            int afterTwoIntervals = testSensor.GetDataCollectionCount();
            Assert.Greater(afterTwoIntervals, afterOneInterval, "应该收集到更多数据");
            
            testSensor.StopSensor();
        }
        
        /// <summary>
        /// 测试位置传感器基础功能
        /// </summary>
        [UnityTest]
        public IEnumerator TestPositionSensorBasics()
        {
            var positionSensor = CreateTestComponent<PositionSensor>();

            // 设置目标对象
            positionSensor.SetTarget(targetTransform, "person", "target_001");

            bool dataReceived = false;
            PositionData receivedData = null;

            // 订阅数据生成事件
            positionSensor.OnDataGenerated += (id, data) =>
            {
                if (data is PositionData posData)
                {
                    dataReceived = true;
                    receivedData = posData;
                }
            };

            // 启动传感器
            positionSensor.StartSensor();
            yield return new WaitForSeconds(0.2f);

            Assert.IsTrue(dataReceived, "应该接收到位置数据");
            Assert.IsNotNull(receivedData, "位置数据不应为空");
            Assert.AreEqual("position", receivedData.sensorType, "传感器类型应该是position");

            positionSensor.StopSensor();
        }
        
        /// <summary>
        /// 测试运动传感器基础功能
        /// </summary>
        [UnityTest]
        public IEnumerator TestMotionSensorBasics()
        {
            var motionSensor = CreateTestComponent<MotionSensor>();

            // 设置目标对象
            motionSensor.SetTarget(targetTransform, "vehicle", "target_001");

            bool dataReceived = false;
            object receivedData = null;

            // 订阅数据生成事件
            motionSensor.OnDataGenerated += (id, data) =>
            {
                dataReceived = true;
                receivedData = data;
            };

            // 移动目标以产生运动数据
            Vector3 initialPosition = targetTransform.position;
            targetTransform.position = initialPosition + Vector3.right * 2f;

            // 启动传感器
            motionSensor.StartSensor();
            yield return new WaitForSeconds(0.2f);

            Assert.IsTrue(dataReceived, "应该接收到运动数据");
            Assert.IsNotNull(receivedData, "运动数据不应为空");

            motionSensor.StopSensor();
        }
        
        /// <summary>
        /// 测试身份传感器基础功能
        /// </summary>
        [UnityTest]
        public IEnumerator TestIdentitySensorBasics()
        {
            var identitySensor = CreateTestComponent<IdentitySensor>();

            // 设置人员信息
            identitySensor.SetPersonInfo("张三", "EMP001", "工人", "建筑部", "CARD001");

            bool dataReceived = false;
            IdentityData receivedData = null;

            // 订阅数据生成事件
            identitySensor.OnDataGenerated += (id, data) =>
            {
                Debug.Log($"OnDataGenerated:{data}");

                if (data is IdentityData idData)
                {
                    dataReceived = true;
                    receivedData = idData;
                }
            };

            // 启动传感器
            identitySensor.StartSensor();
            yield return new WaitForSeconds(5f);

            Assert.IsTrue(dataReceived, "应该接收到身份数据");
            Assert.IsNotNull(receivedData, "身份数据不应为空");
            Assert.AreEqual("identity", receivedData.sensorType, "传感器类型应该是identity");

            identitySensor.StopSensor();
        }
        
        /// <summary>
        /// 测试传感器数据事件触发
        /// </summary>
        [UnityTest]
        public IEnumerator TestSensorDataEventTrigger()
        {   
            bool eventTriggered = false;
            object receivedData = null;

            // 订阅数据生成事件
            testSensor.OnDataGenerated += (id, data) =>
            {
                eventTriggered = true;
                receivedData = data;
            };
            
            // 启动数据收集
            testSensor.StartSensor();
            
            // 等待事件触发
            yield return new WaitForSeconds(0.2f);
            
            Assert.IsTrue(eventTriggered, "数据生成事件应该被触发");
            Assert.IsNotNull(receivedData, "接收到的数据不应为空");
            
            testSensor.StopSensor();
        }
        
        /// <summary>
        /// 测试传感器数据JSON序列化
        /// </summary>
        [Test]
        public void TestSensorDataJsonSerialization()
        {
            // 测试位置数据序列化
            var positionData = new PositionData();
            positionData.position.SetPosition(new Vector3(1, 2, 3));
            positionData.rotation.SetRotation(new Vector3(45, 90, 0));

            AssertJsonSerialization(positionData);

            // 测试身份数据序列化
            var identityData = new IdentityData();
            identityData.name = "测试用户";
            identityData.role = "测试角色";

            AssertJsonSerialization(identityData);
        }

        /// <summary>
        /// 测试噪声传感器基础功能
        /// </summary>
        [UnityTest]
        public IEnumerator TestNoiseSensorBasics()
        {
            var noiseSensor = CreateTestComponent<NoiseSensor>();

            // 验证初始状态
            Assert.IsFalse(noiseSensor.IsRunning, "噪声传感器初始应该未运行");
            Assert.AreEqual("NoiseSensor", noiseSensor.SensorType, "传感器类型应该正确");

            // 启动传感器
            noiseSensor.StartSensor();
            yield return new WaitForSeconds(0.1f);

            Assert.IsTrue(noiseSensor.IsRunning, "噪声传感器应该正在运行");

            // 停止传感器
            noiseSensor.StopSensor();
            yield return new WaitForSeconds(0.1f);

            Assert.IsFalse(noiseSensor.IsRunning, "噪声传感器应该已停止");
        }

        /// <summary>
        /// 测试扬尘传感器基础功能
        /// </summary>
        [UnityTest]
        public IEnumerator TestDustSensorBasics()
        {
            var dustSensor = CreateTestComponent<DustSensor>();

            // 验证初始状态
            Assert.IsFalse(dustSensor.IsRunning, "扬尘传感器初始应该未运行");
            Assert.AreEqual("DustSensor", dustSensor.SensorType, "传感器类型应该正确");

            // 启动传感器
            dustSensor.StartSensor();
            yield return new WaitForSeconds(0.1f);

            Assert.IsTrue(dustSensor.IsRunning, "扬尘传感器应该正在运行");

            // 停止传感器
            dustSensor.StopSensor();
            yield return new WaitForSeconds(0.1f);

            Assert.IsFalse(dustSensor.IsRunning, "扬尘传感器应该已停止");
        }
    }
    
    /// <summary>
    /// 测试用传感器类
    /// </summary>
    public class TestSensor : SensorBase
    {
        private int dataCollectionCount = 0;

        public override string SensorType => "test";

        public float GetDataCollectionInterval() => sampleRate;
        public int GetDataCollectionCount() => dataCollectionCount;

        protected override async UniTask<object> GenerateSensorDataAsync(CancellationToken cancellationToken)
        {
            dataCollectionCount++;

            // 模拟异步数据生成
            await UniTask.Yield(PlayerLoopTiming.Update, cancellationToken);

            // 创建测试数据
            var testData = new TestSensorData(SensorType)
            {
                testValue = dataCollectionCount
            };

            // 触发事件
            TriggerDataGenerated(testData);

            return testData;
        }
    }
    
    /// <summary>
    /// 测试传感器数据类
    /// </summary>
    [System.Serializable]
    public class TestSensorData : SensorDataBase
    {
        public int testValue;
        
        public TestSensorData(string sensorType) 
            : base(sensorType)
        {
        }
    }
}
