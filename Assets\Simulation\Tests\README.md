# Simulation 项目单元测试框架

## 概述

本测试框架为 Simulation 项目提供完整的单元测试支持，基于 Unity Test Framework 和 NUnit，专门针对异步编程和 IoT 系统进行了优化。

## 框架结构

```
Assets/Simulation/Tests/
├── Simulation.Tests.asmdef     # 测试程序集定义
├── TestBase.cs                 # 测试基类
├── TestRunner.cs               # 测试运行器
├── IoTSystemTests.cs           # IoT系统测试
├── SensorSystemTests.cs        # 传感器系统测试
├── SimulatorSystemTests.cs     # 模拟器系统测试
└── README.md                   # 本文档
```

## 核心特性

### 1. 测试基类 (TestBase)
- **统一设置和清理**：自动管理测试对象和资源
- **IoT系统初始化**：自动创建和清理IoT系统实例
- **异步测试支持**：提供UniTask异步操作的测试工具
- **断言增强**：浮点数和Vector3的近似比较
- **事件测试工具**：验证事件触发的便捷方法

### 2. 测试运行器 (TestRunner)
- **便捷执行**：通过Context Menu快速运行测试
- **分类运行**：按模块分别运行测试
- **环境验证**：检查测试环境的完整性
- **自动清理**：清理测试产生的临时对象
- **报告生成**：自动生成测试报告

### 3. 测试覆盖

#### IoT系统测试 (IoTSystemTests)
- ✅ 单例模式验证
- ✅ 传感器注册/注销
- ✅ 重复注册处理
- ✅ 数据收集功能
- ✅ 事件发送机制
- ✅ 空值处理
- ✅ JSON序列化

#### 传感器系统测试 (SensorSystemTests)
- ✅ 传感器配置
- ✅ 启动/停止控制
- ✅ 数据收集间隔
- ✅ 位置数据收集
- ✅ 运动速度计算
- ✅ 身份数据格式
- ✅ 事件触发机制
- ✅ JSON序列化

#### 模拟器系统测试 (SimulatorSystemTests)
- ✅ 启动/停止控制
- ✅ 更新循环验证
- ✅ 工人模拟器基础功能
- ✅ 车辆模拟器基础功能
- ✅ 摄像头模拟器基础功能
- ✅ 事件发送机制
- ✅ 异常处理
- ✅ 并发运行

## 使用指南

### 1. 运行测试

#### 通过Unity Test Runner
1. 打开 `Window > General > Test Runner`
2. 选择 `EditMode` 或 `PlayMode`
3. 点击 `Run All` 或选择特定测试

#### 通过TestRunner组件
1. 在场景中创建GameObject
2. 添加 `TestRunner` 组件
3. 使用Context Menu运行测试：
   - `运行所有测试`
   - `运行IoT系统测试`
   - `运行传感器系统测试`
   - `运行模拟器系统测试`

### 2. 编写新测试

#### 继承TestBase
```csharp
public class MyNewTests : TestBase
{
    [Test]
    public void TestMyFeature()
    {
        // 使用基类提供的工具方法
        var testObject = CreateTestComponent<MyComponent>();
        var sensorId = CreateMockSensorId("my_test");
        
        // 执行测试逻辑
        // ...
        
        // 使用增强断言
        AssertApproximately(expected, actual, 0.01f);
    }
    
    [UnityTest]
    public IEnumerator TestAsyncFeature()
    {
        // 异步测试
        yield return WaitForAsync(MyAsyncMethod());
        
        // 条件等待
        yield return WaitForCondition(() => someCondition, 5f);
    }
}
```

#### 测试异步操作
```csharp
[UnityTest]
public IEnumerator TestAsyncOperation()
{
    bool completed = false;
    
    // 启动异步操作
    MyAsyncMethod().ContinueWith(() => completed = true);
    
    // 等待完成
    yield return WaitForCondition(() => completed, 10f, "异步操作超时");
    
    Assert.IsTrue(completed);
}
```

#### 测试事件系统
```csharp
[Test]
public void TestEventTrigger()
{
    bool eventTriggered = false;

    // 订阅事件
    myComponent.OnSomeEvent += () => eventTriggered = true;

    // 触发操作
    myComponent.TriggerSomeAction();

    // 验证事件
    Assert.IsTrue(eventTriggered);
}

// 使用AssertEventTriggered工具方法
[Test]
public void TestEventTriggerWithHelper()
{
    // 使用工具方法验证事件触发
    AssertEventTriggered<string>(
        handler => myComponent.OnDataReceived += handler,  // 事件订阅
        () => myComponent.SendData("test"),                // 触发操作
        2.0f                                               // 超时时间
    );
}
```

### 3. 测试最佳实践

#### 测试隔离
- 每个测试应该独立运行
- 使用 `SetUp` 和 `TearDown` 进行清理
- 避免测试间的依赖关系

#### 异步测试
- 使用 `WaitForAsync` 处理UniTask
- 设置合理的超时时间
- 正确处理取消令牌

#### 模拟对象
- 创建简单的Mock类进行测试
- 避免依赖外部系统
- 使用依赖注入提高可测试性

#### 断言策略
- 使用具体的断言消息
- 对浮点数使用近似比较
- 验证异常情况的处理

### 4. 调试测试

#### 启用详细日志
```csharp
// 在TestRunner中启用详细日志
enableDetailedLogging = true;
```

#### 使用调试断点
- 在测试方法中设置断点
- 检查变量状态和执行流程
- 验证异步操作的时序

#### 查看测试报告
- 使用TestRunner生成详细报告
- 分析失败的测试用例
- 检查测试覆盖率

## 扩展开发

### 添加新的测试模块
1. 创建新的测试类，继承 `TestBase`
2. 在 `TestRunner` 中添加对应的运行方法
3. 更新测试报告生成逻辑

### 集成测试支持
- 创建集成测试类
- 测试多个组件的协作
- 验证端到端的业务流程

### 性能测试
- 添加性能基准测试
- 监控内存使用情况
- 验证异步操作的性能

## 故障排除

### 常见问题

1. **测试超时**
   - 检查异步操作的取消令牌
   - 增加超时时间设置
   - 验证测试逻辑的正确性

2. **IoT系统初始化失败**
   - 确保场景中没有冲突的IoT系统实例
   - 检查程序集引用是否正确
   - 验证组件依赖关系

3. **异步测试不稳定**
   - 使用适当的等待机制
   - 避免硬编码的时间延迟
   - 正确处理异步操作的生命周期

### 调试技巧
- 使用Unity Console查看测试日志
- 在测试失败时检查Scene视图
- 利用Unity Profiler分析性能问题

## 维护指南

### 定期维护
- 定期运行完整测试套件
- 更新测试用例以覆盖新功能
- 清理过时的测试代码

### 版本兼容性
- 确保测试与Unity版本兼容
- 更新NUnit和UniTask依赖
- 验证新功能的测试覆盖

### 文档更新
- 保持测试文档的最新状态
- 记录新的测试模式和最佳实践
- 更新故障排除指南
