using System.Threading;
using Cysharp.Threading.Tasks;
using UnityEngine;
using Simulation.Data;
using Simulation.Simulators;

namespace Simulation.Sensors
{
    /// <summary>
    /// 身份识别传感器
    /// 负责采集人员和车辆的身份信息
    /// </summary>
    public class IdentitySensor : SensorBase
    {
        [Header("身份信息配置")]
        [SerializeField] private string targetName = "张三";
        [SerializeField] private string role = "普通工人";
        [SerializeField] private string department = "施工部";
        [SerializeField] private string cardId = "CARD001";
        [SerializeField] private string targetType = "person"; // person, vehicle
        [SerializeField] private string status = "active";
        
        [Header("人员信息配置")]
        [SerializeField] private string employeeId = "EMP001";
        [SerializeField] private string phoneNumber = "";
        [SerializeField] private string emergencyContact = "";
        [SerializeField] private string[] certifications;
        [SerializeField] private string workGroup = "A组";
        
        [Header("车辆信息配置")]
        [SerializeField] private string vehicleType = ""; // truck, crane, excavator
        [SerializeField] private string licensePlate = "";
        [SerializeField] private string driverName = "";
        [SerializeField] private string driverId = "";
        [SerializeField] private float vehicleWeight = 0f;
        [SerializeField] private string vehicleModel = "";
        
        // 传感器类型
        public override string SensorType => "identity";
        
        protected override void OnSensorStarted()
        {
            base.OnSensorStarted();
            
            LogDebug($"身份识别传感器启动 - 目标: {targetType}:{targetName}");
        }
        
        protected override async UniTask<object> GenerateSensorDataAsync(CancellationToken cancellationToken)
        {     
            // 创建身份数据
            var identityData = new IdentityData();
            
            // 设置基础身份信息
            identityData.name = targetName;
            identityData.role = role;
            identityData.department = department;
            identityData.cardId = cardId;
            identityData.targetType = targetType;
            identityData.status = status;
            
            return identityData;
        }     
        
        /// <summary>
        /// 检查是否是有效目标
        /// </summary>
        private bool IsValidTarget(Collider collider)
        {
            // 这里可以根据标签、组件或其他条件来判断
            // 简单实现：检查是否有特定的组件或标签
            
            if (targetType == "person")
            {
                return collider.CompareTag("Worker") || collider.GetComponent<WorkerSimulator>() != null;
            }
            else if (targetType == "vehicle")
            {
                return collider.CompareTag("Vehicle") || collider.name.ToLower().Contains("vehicle");
            }
            
            return false;
        }
        
        /// <summary>
        /// 检查是否有视线
        /// </summary>
        private bool HasLineOfSight(Vector3 targetPosition)
        {
            Vector3 direction = (targetPosition - transform.position).normalized;
            float distance = Vector3.Distance(transform.position, targetPosition);
            
            RaycastHit hit;
            if (Physics.Raycast(transform.position, direction, out hit, distance))
            {
                // 如果射线击中的是目标对象，则有视线
                return Vector3.Distance(hit.point, targetPosition) < 0.1f;
            }
            
            return true; // 没有障碍物
        }
        
        /// <summary>
        /// 手动生成数据
        /// </summary>
        private async UniTaskVoid GenerateDataManually()
        {
            var data = await GenerateSensorDataAsync(CancellationToken.None);
            if (data != null)
            {
                TriggerDataGenerated(data);
            }
        }
        
        /// <summary>
        /// 设置人员信息
        /// </summary>
        public void SetPersonInfo(string name, string empId, string role, string dept, string cardId)
        {
            targetName = name;
            employeeId = empId;
            this.role = role;
            department = dept;
            this.cardId = cardId;
            targetType = "person";
            
            LogDebug($"设置人员信息: {name} ({empId})");
        }
        
        /// <summary>
        /// 设置车辆信息
        /// </summary>
        public void SetVehicleInfo(string vehicleType, string plate, string driverName, string driverId)
        {
            this.vehicleType = vehicleType;
            licensePlate = plate;
            this.driverName = driverName;
            this.driverId = driverId;
            targetType = "vehicle";
            targetName = $"{vehicleType}_{plate}";
            
            LogDebug($"设置车辆信息: {vehicleType} ({plate})");
        }
        
        /// <summary>
        /// 更新状态
        /// </summary>
        public void UpdateStatus(string newStatus)
        {
            if (status != newStatus)
            {
                string oldStatus = status;
                status = newStatus;
                LogDebug($"状态更新: {oldStatus} -> {newStatus}");
            }
        }
        
        /// <summary>
        /// 获取完整身份信息
        /// </summary>
        public IdentityData GetFullIdentityInfo()
        {
            var data = new IdentityData();
            data.name = targetName;
            data.role = role;
            data.department = department;
            data.cardId = cardId;
            data.targetType = targetType;
            data.status = status;
            
            return data;
        }
        
        /// <summary>
        /// 检查身份是否有效
        /// </summary>
        public bool IsIdentityValid()
        {
            if (string.IsNullOrEmpty(targetName)) return false;
            if (string.IsNullOrEmpty(cardId)) return false;
            
            if (targetType == "person")
            {
                return !string.IsNullOrEmpty(role) && !string.IsNullOrEmpty(department);
            }
            else if (targetType == "vehicle")
            {
                return !string.IsNullOrEmpty(vehicleType) && !string.IsNullOrEmpty(licensePlate);
            }
            
            return false;
        }
        
        public override bool ValidateConfiguration()
        {
            if (!base.ValidateConfiguration()) return false;
            
            if (string.IsNullOrEmpty(targetName))
            {
                LogError("目标名称不能为空");
                return false;
            }
            
            if (string.IsNullOrEmpty(cardId))
            {
                LogError("卡片ID不能为空");
                return false;
            }
            
            if (targetType == "person" && (string.IsNullOrEmpty(role) || string.IsNullOrEmpty(department)))
            {
                LogError("人员类型需要设置角色和部门");
                return false;
            }
            
            if (targetType == "vehicle" && (string.IsNullOrEmpty(vehicleType) || string.IsNullOrEmpty(licensePlate)))
            {
                LogError("车辆类型需要设置车辆类型和车牌号");
                return false;
            }
            
            return true;
        }
    }
}
