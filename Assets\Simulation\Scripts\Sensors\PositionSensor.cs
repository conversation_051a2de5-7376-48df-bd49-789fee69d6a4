using System.Threading;
using Cysharp.Threading.Tasks;
using UnityEngine;
using Simulation.Data;

namespace Simulation.Sensors
{
    /// <summary>
    /// 位置追踪传感器
    /// 负责采集目标对象的位置和旋转数据
    /// </summary>
    public class PositionSensor : SensorBase
    {
        [Header("位置追踪配置")]
        [SerializeField] private Transform targetTransform;
        [SerializeField] private string targetType = "person"; // person, vehicle, equipment
        [SerializeField] private string targetId = "target_001";
        [SerializeField] private string zone = "construction_area_A";
        
        [Header("数据采集配置")]
        [SerializeField] private bool enablePositionX = true;
        [SerializeField] private bool enablePositionY = true;
        [SerializeField] private bool enablePositionZ = true;
        [SerializeField] private bool enableRotationX = false;
        [SerializeField] private bool enableRotationY = true;
        [SerializeField] private bool enableRotationZ = false;
        
        [Header("精度配置")]
        [SerializeField] private int positionDecimalPlaces = 3;
        [SerializeField] private int rotationDecimalPlaces = 1;
        [SerializeField] private float positionThreshold = 0.01f; // 位置变化阈值
        [SerializeField] private float rotationThreshold = 1f; // 旋转变化阈值（度）
        
        [Header("坐标系配置")]
        [SerializeField] private bool useWorldCoordinates = true;
        [SerializeField] private Transform referenceTransform; // 参考坐标系
        
        // 上次记录的数据
        private Vector3 lastPosition;
        private Vector3 lastRotation;
        private bool isFirstSample = true;
        
        // 传感器类型
        public override string SensorType => "position";
        
        protected override void OnSensorStarted()
        {
            base.OnSensorStarted();
            
            // 如果没有设置目标对象，使用自身
            if (targetTransform == null)
            {
                targetTransform = transform;
                LogDebug("未设置目标对象，使用自身Transform");
            }
            
            // 如果没有设置参考坐标系，使用世界坐标系
            if (!useWorldCoordinates && referenceTransform == null)
            {
                referenceTransform = transform.parent;
                LogDebug("使用父对象作为参考坐标系");
            }
            
            // 初始化上次记录的数据
            lastPosition = GetCurrentPosition();
            lastRotation = GetCurrentRotation();
            isFirstSample = true;
            
            LogDebug($"位置传感器启动 - 目标: {targetType}:{targetId}, 区域: {zone}");
        }
        
        protected override async UniTask<object> GenerateSensorDataAsync(CancellationToken cancellationToken)
        {
            if (targetTransform == null)
            {
                LogError("目标对象为空，无法采集位置数据");
                return null;
            }
            
            Vector3 currentPosition = GetCurrentPosition();
            Vector3 currentRotation = GetCurrentRotation();
            
            // 检查是否有足够的变化来发送数据
            if (!isFirstSample && !HasSignificantChange(currentPosition, currentRotation))
            {
                return null; // 没有显著变化，不发送数据
            }
            
            // 创建位置数据
            var positionData = new PositionData();
            
            // 设置目标信息
            positionData.targetType = targetType;
            positionData.targetId = targetId;
            positionData.zone = zone;
            
            // 设置位置数据
            SetPositionData(positionData.position, currentPosition);
            
            // 设置旋转数据
            SetRotationData(positionData.rotation, currentRotation);
            
            // 更新上次记录的数据
            lastPosition = currentPosition;
            lastRotation = currentRotation;
            isFirstSample = false;
            
            return positionData;
        }
        
        /// <summary>
        /// 获取当前位置
        /// </summary>
        private Vector3 GetCurrentPosition()
        {
            if (useWorldCoordinates)
            {
                return targetTransform.position;
            }
            else if (referenceTransform != null)
            {
                return referenceTransform.InverseTransformPoint(targetTransform.position);
            }
            else
            {
                return targetTransform.localPosition;
            }
        }
        
        /// <summary>
        /// 获取当前旋转
        /// </summary>
        private Vector3 GetCurrentRotation()
        {
            if (useWorldCoordinates)
            {
                return targetTransform.eulerAngles;
            }
            else if (referenceTransform != null)
            {
                Quaternion relativeRotation = Quaternion.Inverse(referenceTransform.rotation) * targetTransform.rotation;
                return relativeRotation.eulerAngles;
            }
            else
            {
                return targetTransform.localEulerAngles;
            }
        }
        
        /// <summary>
        /// 检查是否有显著变化
        /// </summary>
        private bool HasSignificantChange(Vector3 currentPosition, Vector3 currentRotation)
        {
            // 检查位置变化
            float positionDistance = Vector3.Distance(currentPosition, lastPosition);
            if (positionDistance >= positionThreshold)
            {
                return true;
            }
            
            // 检查旋转变化
            float rotationDifference = Quaternion.Angle(
                Quaternion.Euler(currentRotation),
                Quaternion.Euler(lastRotation)
            );
            if (rotationDifference >= rotationThreshold)
            {
                return true;
            }
            
            return false;
        }
        
        /// <summary>
        /// 设置位置数据
        /// </summary>
        private void SetPositionData(PositionInfo positionInfo, Vector3 position)
        {
            if (enablePositionX)
            {
                positionInfo.x = RoundToDecimalPlaces(position.x, positionDecimalPlaces);
            }
            
            if (enablePositionY)
            {
                positionInfo.y = RoundToDecimalPlaces(position.y, positionDecimalPlaces);
            }
            
            if (enablePositionZ)
            {
                positionInfo.z = RoundToDecimalPlaces(position.z, positionDecimalPlaces);
            }
        }
        
        /// <summary>
        /// 设置旋转数据
        /// </summary>
        private void SetRotationData(RotationInfo rotationInfo, Vector3 rotation)
        {
            if (enableRotationX)
            {
                rotationInfo.x = RoundToDecimalPlaces(rotation.x, rotationDecimalPlaces);
            }
            
            if (enableRotationY)
            {
                rotationInfo.y = RoundToDecimalPlaces(rotation.y, rotationDecimalPlaces);
            }
            
            if (enableRotationZ)
            {
                rotationInfo.z = RoundToDecimalPlaces(rotation.z, rotationDecimalPlaces);
            }
        }
        
        /// <summary>
        /// 四舍五入到指定小数位
        /// </summary>
        private float RoundToDecimalPlaces(float value, int decimalPlaces)
        {
            float multiplier = Mathf.Pow(10f, decimalPlaces);
            return Mathf.Round(value * multiplier) / multiplier;
        }
        
        /// <summary>
        /// 设置目标对象
        /// </summary>
        public void SetTarget(Transform target, string type, string id)
        {
            targetTransform = target;
            targetType = type;
            targetId = id;
            
            LogDebug($"设置新目标: {type}:{id}");
        }
        
        /// <summary>
        /// 设置区域
        /// </summary>
        public void SetZone(string newZone)
        {
            zone = newZone;
            LogDebug($"设置新区域: {zone}");
        }
        
        /// <summary>
        /// 设置位置采集配置
        /// </summary>
        public void SetPositionConfig(bool x, bool y, bool z)
        {
            enablePositionX = x;
            enablePositionY = y;
            enablePositionZ = z;
            
            LogDebug($"位置采集配置: X={x}, Y={y}, Z={z}");
        }
        
        /// <summary>
        /// 设置旋转采集配置
        /// </summary>
        public void SetRotationConfig(bool x, bool y, bool z)
        {
            enableRotationX = x;
            enableRotationY = y;
            enableRotationZ = z;
            
            LogDebug($"旋转采集配置: X={x}, Y={y}, Z={z}");
        }
        
        /// <summary>
        /// 设置精度配置
        /// </summary>
        public void SetPrecisionConfig(int positionDecimals, int rotationDecimals)
        {
            positionDecimalPlaces = Mathf.Clamp(positionDecimals, 0, 6);
            rotationDecimalPlaces = Mathf.Clamp(rotationDecimals, 0, 3);
            
            LogDebug($"精度配置: 位置={positionDecimalPlaces}位, 旋转={rotationDecimalPlaces}位");
        }
        
        /// <summary>
        /// 设置变化阈值
        /// </summary>
        public void SetThresholds(float positionThresh, float rotationThresh)
        {
            positionThreshold = Mathf.Max(0f, positionThresh);
            rotationThreshold = Mathf.Max(0f, rotationThresh);
            
            LogDebug($"阈值配置: 位置={positionThreshold}m, 旋转={rotationThreshold}°");
        }
        
        /// <summary>
        /// 强制发送当前位置数据
        /// </summary>
        public async UniTask<PositionData> ForceGetCurrentData()
        {
            if (targetTransform == null) return null;
            
            var data = await GenerateSensorDataAsync(CancellationToken.None) as PositionData;
            return data;
        }
        
        /// <summary>
        /// 获取当前目标距离
        /// </summary>
        public float GetDistanceFromReference(Vector3 referencePoint)
        {
            if (targetTransform == null) return float.MaxValue;
            
            return Vector3.Distance(targetTransform.position, referencePoint);
        }
        
        /// <summary>
        /// 检查目标是否在指定区域内
        /// </summary>
        public bool IsTargetInBounds(Bounds bounds)
        {
            if (targetTransform == null) return false;
            
            return bounds.Contains(targetTransform.position);
        }
        
        public override bool ValidateConfiguration()
        {
            if (!base.ValidateConfiguration()) return false;
            
            if (targetTransform == null)
            {
                LogError("目标对象不能为空");
                return false;
            }
            
            if (string.IsNullOrEmpty(targetType))
            {
                LogError("目标类型不能为空");
                return false;
            }
            
            if (string.IsNullOrEmpty(targetId))
            {
                LogError("目标ID不能为空");
                return false;
            }
            
            if (positionThreshold < 0 || rotationThreshold < 0)
            {
                LogError("阈值不能为负数");
                return false;
            }
            
            return true;
        }
        
        /// <summary>
        /// 绘制调试信息
        /// </summary>
        private void OnDrawGizmos()
        {
            if (targetTransform == null) return;
            
            // 绘制目标位置
            Gizmos.color = Color.green;
            Gizmos.DrawWireSphere(targetTransform.position, 0.2f);
            
            // 绘制坐标轴
            Gizmos.color = Color.red;
            Gizmos.DrawRay(targetTransform.position, targetTransform.right * 0.5f);
            Gizmos.color = Color.green;
            Gizmos.DrawRay(targetTransform.position, targetTransform.up * 0.5f);
            Gizmos.color = Color.blue;
            Gizmos.DrawRay(targetTransform.position, targetTransform.forward * 0.5f);
            
            // 绘制传感器位置
            if (targetTransform != transform)
            {
                Gizmos.color = Color.yellow;
                Gizmos.DrawWireSphere(transform.position, 0.1f);
                Gizmos.DrawLine(transform.position, targetTransform.position);
            }
        }
    }
}
