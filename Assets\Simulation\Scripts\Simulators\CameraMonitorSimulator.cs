using System;
using System.Collections.Generic;
using System.Threading;
using Cysharp.Threading.Tasks;
using UnityEngine;
using Simulation.IoT;
using Simulation.Data;

namespace Simulation.Simulators
{
    /// <summary>
    /// 监控摄像头模拟器
    /// 模拟监控摄像头的运行状态、视角控制和监控事件检测
    /// </summary>
    public class CameraMonitorSimulator : SimulatorBase
    {
        [Header("摄像头基础信息")]
        [SerializeField] private string cameraId = "camera_001";
        [SerializeField] private string cameraName = "主入口监控";
        [SerializeField] private CameraType cameraType = CameraType.Fixed;
        [SerializeField] private string installationLocation = "主入口";
        
        [Header("摄像头技术参数")]
        [SerializeField] private string resolution = "1920x1080";
        [SerializeField] private int frameRate = 30;
        [SerializeField] private float viewDistance = 50f;
        [SerializeField] private float horizontalFOV = 90f; // 水平视角
        [SerializeField] private float verticalFOV = 60f; // 垂直视角
        
        [Header("云台摄像头参数（仅云台类型）")]
        [SerializeField] private bool enablePTZControl = false;
        [SerializeField] private float minHorizontalAngle = -180f;
        [SerializeField] private float maxHorizontalAngle = 180f;
        [SerializeField] private float minVerticalAngle = -30f;
        [SerializeField] private float maxVerticalAngle = 90f;
        [SerializeField] private float minZoomLevel = 1f;
        [SerializeField] private float maxZoomLevel = 10f;
        
        [Header("监控配置")]
        [SerializeField] private float monitoringRadius = 30f; // 监控半径
        [SerializeField] private LayerMask detectionLayers = -1; // 检测层
        [SerializeField] private bool enableMotionDetection = true;
        [SerializeField] private bool enableIntrusionDetection = true;
        [SerializeField] private bool enableObjectDetection = false;
        
        [Header("数据上报配置")]
        [SerializeField] private float statusReportInterval = 60f; // 状态上报间隔（秒）
        [SerializeField] private float detectionCheckInterval = 2f; // 检测检查间隔（秒）
        [SerializeField] private bool enableDataLogging = true;
        
        [Header("录像配置")]
        [SerializeField] private bool enableRecording = true;
        [SerializeField] private float storageCapacity = 1000f; // 存储容量（GB）
        [SerializeField] private float recordingQuality = 0.8f; // 录像质量（0-1）
        [SerializeField] private bool enableEventTriggeredRecording = true;
        
        [Header("可视化配置")]
        [SerializeField] private bool showMonitoringRange = true;
        [SerializeField] private Color monitoringRangeColor = Color.green;
        [SerializeField] private bool showViewDirection = true;
        [SerializeField] private Color viewDirectionColor = Color.blue;
        
        // 私有字段
        private CameraData currentCameraData;
        private List<GameObject> detectedObjects = new List<GameObject>();
        private float lastStatusReportTime;
        private float lastDetectionCheckTime;
        private float recordingStartTime;
        private bool isRecording;
        
        // 云台控制相关
        private float currentHorizontalAngle;
        private float currentVerticalAngle;
        private float currentZoomLevel = 1f;
        private bool isPTZMoving;
        
        // 事件统计
        private int motionDetectionCount;
        private int intrusionDetectionCount;
        private int objectDetectionCount;
        
        /// <summary>
        /// 摄像头类型枚举
        /// </summary>
        public enum CameraType
        {
            Fixed,  // 固定摄像头
            PTZ     // 云台摄像头
        }
        
        protected override async UniTask OnStartSimulationAsync(CancellationToken cancellationToken)
        {
            Debug.Log($"[CameraMonitor] 启动监控摄像头模拟器: {cameraName} ({cameraId})");
            
            // 初始化摄像头数据
            InitializeCameraData();
            
            // 启动录像（如果启用）
            if (enableRecording)
            {
                StartRecording();
            }
            
            // 注册到IoT系统
            RegisterToIoTSystem();
            
            // 初始化云台位置
            if (cameraType == CameraType.PTZ && enablePTZControl)
            {
                InitializePTZPosition();
            }
            
            Debug.Log($"[CameraMonitor] 摄像头 {cameraName} 初始化完成");
        }
        
        protected override void OnSimulationUpdate(float deltaTime)
        {
            var currentTime = Time.time;
            
            // 状态上报
            if (currentTime - lastStatusReportTime >= statusReportInterval)
            {
                ReportCameraStatus();
                lastStatusReportTime = currentTime;
            }
            
            // 检测监控事件
            if (currentTime - lastDetectionCheckTime >= detectionCheckInterval)
            {
                PerformDetectionCheck();
                lastDetectionCheckTime = currentTime;
            }
            
            // 更新录像状态
            if (isRecording)
            {
                UpdateRecordingStatus(deltaTime);
            }
            
            // 更新云台状态
            if (cameraType == CameraType.PTZ && enablePTZControl)
            {
                UpdatePTZStatus(deltaTime);
            }
            
            // 更新摄像头数据
            UpdateCameraData();
        }
        
        protected override async UniTask OnStopSimulationAsync()
        {
            Debug.Log($"[CameraMonitor] 停止监控摄像头模拟器: {cameraName}");
            
            // 停止录像
            if (isRecording)
            {
                StopRecording();
            }
            
            // 发送停机事件
            SendCameraEvent("camera_shutdown", "摄像头正常关机", 1.0f);
            
            Debug.Log($"[CameraMonitor] 摄像头 {cameraName} 已停止");
        }
        
        /// <summary>
        /// 初始化摄像头数据
        /// </summary>
        private void InitializeCameraData()
        {
            currentCameraData = new CameraData()
            {
                cameraType = cameraType == CameraType.Fixed ? "fixed" : "ptz"
            };
            
            // 设置位置信息
            currentCameraData.location.SetPosition(transform.position);
            
            // 设置视角信息
            currentCameraData.viewInfo.resolution = resolution;
            currentCameraData.viewInfo.frameRate = frameRate;
            currentCameraData.viewInfo.viewDistance = viewDistance;
            currentCameraData.viewInfo.horizontalAngle = transform.eulerAngles.y;
            currentCameraData.viewInfo.verticalAngle = transform.eulerAngles.x;
            currentCameraData.viewInfo.zoomLevel = currentZoomLevel;
            
            // 设置录像信息
            currentCameraData.recordingInfo.storageCapacity = storageCapacity;
            currentCameraData.recordingInfo.storageLocation = $"/storage/{cameraId}/";
            
            // 设置状态信息
            currentCameraData.status.isOnline = true;
            currentCameraData.status.operationMode = "normal";
            currentCameraData.status.signalStrength = UnityEngine.Random.Range(85f, 100f);
            currentCameraData.status.lastMaintenanceTime = DateTime.Now.AddDays(-UnityEngine.Random.Range(1, 30)).ToString("yyyy-MM-dd");
        }
        
        /// <summary>
        /// 注册到IoT系统
        /// </summary>
        private void RegisterToIoTSystem()
        {
            try
            {
                IoTSystem.Instance.CollectSensorData(cameraId, currentCameraData);
                Debug.Log($"[CameraMonitor] 摄像头 {cameraName} 已注册到IoT系统");
            }
            catch (Exception ex)
            {
                Debug.LogError($"[CameraMonitor] 注册到IoT系统失败: {ex.Message}");
            }
        }
        
        /// <summary>
        /// 上报摄像头状态
        /// </summary>
        private void ReportCameraStatus()
        {
            if (!enableDataLogging) return;
            
            try
            {
                UpdateCameraData();
                IoTSystem.Instance.CollectSensorData(cameraId, currentCameraData);
                
                Debug.Log($"[CameraMonitor] 摄像头 {cameraName} 状态已上报 - 在线: {currentCameraData.status.isOnline}, " +
                         $"录像: {currentCameraData.status.isRecording}, 信号: {currentCameraData.status.signalStrength:F1}%");
            }
            catch (Exception ex)
            {
                Debug.LogError($"[CameraMonitor] 状态上报失败: {ex.Message}");
            }
        }
        
        /// <summary>
        /// 更新摄像头数据
        /// </summary>
        private void UpdateCameraData()
        {
            if (currentCameraData == null) return;
            
            // 更新时间戳
            currentCameraData.timestamp = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss");
            
            // 更新位置（如果摄像头移动了）
            currentCameraData.location.SetPosition(transform.position);
            
            // 更新视角信息
            currentCameraData.viewInfo.horizontalAngle = currentHorizontalAngle;
            currentCameraData.viewInfo.verticalAngle = currentVerticalAngle;
            currentCameraData.viewInfo.zoomLevel = currentZoomLevel;
            
            // 更新录像状态
            currentCameraData.status.isRecording = isRecording;
            currentCameraData.recordingInfo.isRecording = isRecording;
            
            if (isRecording)
            {
                currentCameraData.recordingInfo.recordingDuration = Time.time - recordingStartTime;
                // 模拟存储使用量增长
                float recordingRate = 0.1f; // GB/小时
                currentCameraData.recordingInfo.storageUsed += recordingRate * Time.deltaTime / 3600f;
            }
            
            // 模拟信号强度波动
            currentCameraData.status.signalStrength = Mathf.Clamp(
                currentCameraData.status.signalStrength + UnityEngine.Random.Range(-2f, 2f),
                70f, 100f
            );
        }

        /// <summary>
        /// 执行检测检查
        /// </summary>
        private void PerformDetectionCheck()
        {
            if (!currentCameraData.status.isOnline) return;

            // 检测监控范围内的对象
            var colliders = Physics.OverlapSphere(transform.position, monitoringRadius, detectionLayers);
            var currentDetectedObjects = new List<GameObject>();

            foreach (var collider in colliders)
            {
                if (collider.gameObject == gameObject) continue; // 排除自己

                // 检查是否在视角范围内
                if (IsInViewRange(collider.transform.position))
                {
                    currentDetectedObjects.Add(collider.gameObject);

                    // 检查是否是新检测到的对象
                    if (!detectedObjects.Contains(collider.gameObject))
                    {
                        OnObjectDetected(collider.gameObject);
                    }
                }
            }

            // 检查离开监控范围的对象
            foreach (var obj in detectedObjects)
            {
                if (obj != null && !currentDetectedObjects.Contains(obj))
                {
                    OnObjectLeft(obj);
                }
            }

            detectedObjects = currentDetectedObjects;
        }

        /// <summary>
        /// 检查位置是否在视角范围内
        /// </summary>
        private bool IsInViewRange(Vector3 targetPosition)
        {
            Vector3 directionToTarget = (targetPosition - transform.position).normalized;
            Vector3 cameraForward = transform.forward;

            // 计算水平角度
            float horizontalAngle = Vector3.Angle(
                new Vector3(cameraForward.x, 0, cameraForward.z),
                new Vector3(directionToTarget.x, 0, directionToTarget.z)
            );

            // 计算垂直角度
            float verticalAngle = Vector3.Angle(cameraForward, directionToTarget);

            // 检查是否在视角范围内
            return horizontalAngle <= horizontalFOV / 2f && verticalAngle <= verticalFOV / 2f;
        }

        /// <summary>
        /// 当检测到对象时调用
        /// </summary>
        private void OnObjectDetected(GameObject detectedObject)
        {
            string objectType = GetObjectType(detectedObject);

            if (enableMotionDetection)
            {
                motionDetectionCount++;
                SendCameraEvent("motion_detected", $"检测到移动目标: {objectType}", 0.8f, detectedObject.transform.position);
            }

            if (enableIntrusionDetection && IsIntrusionDetection(detectedObject))
            {
                intrusionDetectionCount++;
                SendCameraEvent("intrusion_detected", $"检测到入侵: {objectType}", 0.9f, detectedObject.transform.position);
            }

            if (enableObjectDetection)
            {
                objectDetectionCount++;
                SendCameraEvent("object_detected", $"检测到对象: {objectType}", 0.7f, detectedObject.transform.position);
            }

            // 如果启用事件触发录像且当前未录像，则开始录像
            if (enableEventTriggeredRecording && !isRecording)
            {
                StartRecording();
            }
        }

        /// <summary>
        /// 当对象离开监控范围时调用
        /// </summary>
        private void OnObjectLeft(GameObject leftObject)
        {
            string objectType = GetObjectType(leftObject);
            SendCameraEvent("object_left", $"目标离开监控范围: {objectType}", 0.6f, leftObject.transform.position);
        }

        /// <summary>
        /// 获取对象类型
        /// </summary>
        private string GetObjectType(GameObject obj)
        {
            // 根据标签或组件判断对象类型
            if (obj.CompareTag("Worker")) return "工人";
            if (obj.CompareTag("Vehicle")) return "车辆";
            if (obj.CompareTag("Equipment")) return "设备";
            if (obj.name.Contains("Crane")) return "塔吊";
            if (obj.name.Contains("Elevator")) return "升降机";
            return "未知对象";
        }

        /// <summary>
        /// 判断是否为入侵检测
        /// </summary>
        private bool IsIntrusionDetection(GameObject obj)
        {
            // 简单的入侵判断逻辑，可以根据需要扩展
            return obj.CompareTag("Unauthorized") ||
                   (obj.CompareTag("Worker") && UnityEngine.Random.value < 0.1f); // 10%概率判定为入侵
        }

        /// <summary>
        /// 发送摄像头事件
        /// </summary>
        private void SendCameraEvent(string eventType, string description, float confidence, Vector3? location = null)
        {
            try
            {
                var eventData = new MonitoringEventData
                {
                    eventId = Guid.NewGuid().ToString(),
                    eventType = eventType,
                    sourceId = cameraId,
                    timestamp = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"),
                    title = $"监控事件 - {cameraName}",
                    description = description,
                    cameraId = cameraId,
                    detectionType = eventType,
                    confidence = confidence,
                    imageUrl = $"/images/{cameraId}/{DateTime.Now:yyyyMMdd_HHmmss}.jpg"
                };

                if (location.HasValue)
                {
                    eventData.detectionLocation.SetPosition(location.Value);
                }

                IoTSystem.Instance.SendIOTEvent(cameraId, eventType, eventData.title, eventData.description, eventData);

                Debug.Log($"[CameraMonitor] 发送监控事件: {description} (置信度: {confidence:P0})");
            }
            catch (Exception ex)
            {
                Debug.LogError($"[CameraMonitor] 发送事件失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 开始录像
        /// </summary>
        private void StartRecording()
        {
            if (isRecording) return;

            isRecording = true;
            recordingStartTime = Time.time;
            currentCameraData.recordingInfo.recordingStartTime = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss");

            SendCameraEvent("recording_started", "开始录像", 1.0f);
            Debug.Log($"[CameraMonitor] 摄像头 {cameraName} 开始录像");
        }

        /// <summary>
        /// 停止录像
        /// </summary>
        private void StopRecording()
        {
            if (!isRecording) return;

            isRecording = false;
            float recordingDuration = Time.time - recordingStartTime;

            SendCameraEvent("recording_stopped", $"停止录像，时长: {recordingDuration:F1}秒", 1.0f);
            Debug.Log($"[CameraMonitor] 摄像头 {cameraName} 停止录像，时长: {recordingDuration:F1}秒");
        }

        /// <summary>
        /// 更新录像状态
        /// </summary>
        private void UpdateRecordingStatus(float deltaTime)
        {
            // 检查存储空间
            if (currentCameraData.recordingInfo.storageUsed >= currentCameraData.recordingInfo.storageCapacity * 0.95f)
            {
                StopRecording();
                SendCameraEvent("storage_full", "存储空间不足，自动停止录像", 1.0f);
            }
        }

        /// <summary>
        /// 初始化云台位置
        /// </summary>
        private void InitializePTZPosition()
        {
            currentHorizontalAngle = transform.eulerAngles.y;
            currentVerticalAngle = transform.eulerAngles.x;
            currentZoomLevel = 1f;
            isPTZMoving = false;

            Debug.Log($"[CameraMonitor] 云台摄像头 {cameraName} 初始位置: H={currentHorizontalAngle:F1}°, V={currentVerticalAngle:F1}°, Z={currentZoomLevel:F1}x");
        }

        /// <summary>
        /// 更新云台状态
        /// </summary>
        private void UpdatePTZStatus(float deltaTime)
        {
            // 模拟自动巡航
            if (!isPTZMoving && UnityEngine.Random.value < 0.01f) // 1%概率开始巡航
            {
                StartPTZPatrol();
            }

            // 更新云台位置
            if (isPTZMoving)
            {
                UpdatePTZMovement(deltaTime);
            }
        }

        /// <summary>
        /// 开始云台巡航
        /// </summary>
        private void StartPTZPatrol()
        {
            isPTZMoving = true;

            // 随机选择新的目标位置
            float targetHorizontal = UnityEngine.Random.Range(minHorizontalAngle, maxHorizontalAngle);
            float targetVertical = UnityEngine.Random.Range(minVerticalAngle, maxVerticalAngle);
            float targetZoom = UnityEngine.Random.Range(minZoomLevel, maxZoomLevel);

            SendCameraEvent("ptz_patrol_started", $"开始巡航 - 目标位置: H={targetHorizontal:F1}°, V={targetVertical:F1}°, Z={targetZoom:F1}x", 1.0f);

            Debug.Log($"[CameraMonitor] 云台摄像头 {cameraName} 开始巡航");
        }

        /// <summary>
        /// 更新云台移动
        /// </summary>
        private void UpdatePTZMovement(float deltaTime)
        {
            // 简单的移动模拟，实际应用中会有更复杂的控制逻辑
            float moveSpeed = 30f; // 度/秒
            float zoomSpeed = 2f; // 倍数/秒

            // 模拟移动到随机位置
            if (UnityEngine.Random.value < 0.1f) // 10%概率停止移动
            {
                isPTZMoving = false;
                SendCameraEvent("ptz_patrol_completed", "巡航完成", 1.0f);
                Debug.Log($"[CameraMonitor] 云台摄像头 {cameraName} 巡航完成");
            }
        }

        /// <summary>
        /// 手动控制云台
        /// </summary>
        public void ControlPTZ(float horizontalAngle, float verticalAngle, float zoomLevel)
        {
            if (cameraType != CameraType.PTZ || !enablePTZControl) return;

            // 限制角度范围
            horizontalAngle = Mathf.Clamp(horizontalAngle, minHorizontalAngle, maxHorizontalAngle);
            verticalAngle = Mathf.Clamp(verticalAngle, minVerticalAngle, maxVerticalAngle);
            zoomLevel = Mathf.Clamp(zoomLevel, minZoomLevel, maxZoomLevel);

            currentHorizontalAngle = horizontalAngle;
            currentVerticalAngle = verticalAngle;
            currentZoomLevel = zoomLevel;

            // 更新Transform
            transform.rotation = Quaternion.Euler(verticalAngle, horizontalAngle, 0);

            SendCameraEvent("ptz_manual_control", $"手动控制云台 - H={horizontalAngle:F1}°, V={verticalAngle:F1}°, Z={zoomLevel:F1}x", 1.0f);

            Debug.Log($"[CameraMonitor] 手动控制云台摄像头 {cameraName}: H={horizontalAngle:F1}°, V={verticalAngle:F1}°, Z={zoomLevel:F1}x");
        }

        /// <summary>
        /// 获取当前监控统计信息
        /// </summary>
        public string GetMonitoringStatistics()
        {
            return $"监控统计 - 运动检测: {motionDetectionCount}, 入侵检测: {intrusionDetectionCount}, 对象检测: {objectDetectionCount}";
        }

        /// <summary>
        /// 在Scene视图中绘制监控范围
        /// </summary>
        private void OnDrawGizmosSelected()
        {
            if (!showMonitoringRange && !showViewDirection) return;

            // 绘制监控范围
            if (showMonitoringRange)
            {
                Gizmos.color = monitoringRangeColor;
                Gizmos.DrawWireSphere(transform.position, monitoringRadius);
            }

            // 绘制视角方向
            if (showViewDirection)
            {
                Gizmos.color = viewDirectionColor;
                Vector3 forward = transform.forward * viewDistance;
                Gizmos.DrawRay(transform.position, forward);

                // 绘制视角锥体
                float halfHorizontalFOV = horizontalFOV * 0.5f * Mathf.Deg2Rad;
                float halfVerticalFOV = verticalFOV * 0.5f * Mathf.Deg2Rad;

                Vector3 rightDirection = Quaternion.AngleAxis(horizontalFOV * 0.5f, transform.up) * transform.forward;
                Vector3 leftDirection = Quaternion.AngleAxis(-horizontalFOV * 0.5f, transform.up) * transform.forward;
                Vector3 upDirection = Quaternion.AngleAxis(verticalFOV * 0.5f, transform.right) * transform.forward;
                Vector3 downDirection = Quaternion.AngleAxis(-verticalFOV * 0.5f, transform.right) * transform.forward;

                Gizmos.DrawRay(transform.position, rightDirection * viewDistance);
                Gizmos.DrawRay(transform.position, leftDirection * viewDistance);
                Gizmos.DrawRay(transform.position, upDirection * viewDistance);
                Gizmos.DrawRay(transform.position, downDirection * viewDistance);
            }
        }
    }
}
