using System;
using NUnit.Framework;
using UnityEngine;
using Simulation.Simulators;

namespace Simulation.Tests
{
    /// <summary>
    /// 测试验证类 - 验证新增的模拟器测试是否能正常编译和运行
    /// </summary>
    public class TestValidation
    {
        /// <summary>
        /// 验证所有新增模拟器类是否可以实例化
        /// </summary>
        [Test]
        public void TestSimulatorClassesCanBeInstantiated()
        {
            // 验证塔吊模拟器
            Assert.DoesNotThrow(() => {
                var crane = typeof(CraneSimulator);
                Assert.IsNotNull(crane, "CraneSimulator类应该存在");
            });

            // 验证升降机模拟器
            Assert.DoesNotThrow(() => {
                var elevator = typeof(ElevatorSimulator);
                Assert.IsNotNull(elevator, "ElevatorSimulator类应该存在");
            });

            // 验证环境监测模拟器
            Assert.DoesNotThrow(() => {
                var environmental = typeof(EnvironmentalMonitorSimulator);
                Assert.IsNotNull(environmental, "EnvironmentalMonitorSimulator类应该存在");
            });

            // 验证物料管理模拟器
            Assert.DoesNotThrow(() => {
                var material = typeof(MaterialManagementSimulator);
                Assert.IsNotNull(material, "MaterialManagementSimulator类应该存在");
            });

            // 验证天气模拟器
            Assert.DoesNotThrow(() => {
                var weather = typeof(WeatherSimulator);
                Assert.IsNotNull(weather, "WeatherSimulator类应该存在");
            });
        }

        /// <summary>
        /// 验证所有状态枚举是否存在
        /// </summary>
        [Test]
        public void TestStateEnumsExist()
        {
            // 验证塔吊状态枚举
            Assert.DoesNotThrow(() => {
                var craneState = typeof(CraneState);
                Assert.IsNotNull(craneState, "CraneState枚举应该存在");
            });

            // 验证升降机状态枚举
            Assert.DoesNotThrow(() => {
                var elevatorState = typeof(ElevatorState);
                Assert.IsNotNull(elevatorState, "ElevatorState枚举应该存在");
            });

            // 验证监测状态枚举
            Assert.DoesNotThrow(() => {
                var monitoringState = typeof(MonitoringState);
                Assert.IsNotNull(monitoringState, "MonitoringState枚举应该存在");
            });

            // 验证天气类型枚举
            Assert.DoesNotThrow(() => {
                var weatherType = typeof(WeatherType);
                Assert.IsNotNull(weatherType, "WeatherType枚举应该存在");
            });
        }

        /// <summary>
        /// 验证所有数据结构是否存在
        /// </summary>
        [Test]
        public void TestDataStructuresExist()
        {
            // 验证塔吊作业数据
            Assert.DoesNotThrow(() => {
                var craneOperation = typeof(CraneOperation);
                Assert.IsNotNull(craneOperation, "CraneOperation类应该存在");
            });

            // 验证升降机相关数据
            Assert.DoesNotThrow(() => {
                var elevatorPassenger = typeof(ElevatorPassenger);
                var elevatorRequest = typeof(ElevatorRequest);
                Assert.IsNotNull(elevatorPassenger, "ElevatorPassenger类应该存在");
                Assert.IsNotNull(elevatorRequest, "ElevatorRequest类应该存在");
            });

            // 验证环境数据
            Assert.DoesNotThrow(() => {
                var environmentalData = typeof(EnvironmentalData);
                Assert.IsNotNull(environmentalData, "EnvironmentalData类应该存在");
            });

            // 验证物料相关数据
            Assert.DoesNotThrow(() => {
                var materialTypeConfig = typeof(MaterialTypeConfig);
                var materialInventory = typeof(MaterialInventory);
                var materialBatch = typeof(MaterialBatch);
                Assert.IsNotNull(materialTypeConfig, "MaterialTypeConfig类应该存在");
                Assert.IsNotNull(materialInventory, "MaterialInventory类应该存在");
                Assert.IsNotNull(materialBatch, "MaterialBatch类应该存在");
            });
        }
    }
}
