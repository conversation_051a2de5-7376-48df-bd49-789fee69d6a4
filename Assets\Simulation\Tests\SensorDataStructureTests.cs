using System;
using NUnit.Framework;
using UnityEngine;
using Simulation.Sensors;
using Simulation.Data;

namespace Simulation.Tests
{
    /// <summary>
    /// 传感器数据结构测试类
    /// 测试NoiseSensorData和DustSensorData的数据结构完整性
    /// </summary>
    public class SensorDataStructureTests : TestBase
    {
        #region 噪声传感器数据结构测试

        /// <summary>
        /// 测试噪声传感器数据结构基础功能
        /// </summary>
        [Test]
        public void TestNoiseSensorDataStructure()
        {
            var noiseData = new NoiseSensorData
            {
                timestamp = DateTime.UtcNow,
                sensorId = "noise_001",
                deviceId = "device_001",
                noiseLevel = 75.5f,
                unit = "dB",
                isThresholdExceeded = true,
                threshold = 70f,
                location = new Vector3(10f, 5f, 15f)
            };
            
            // 验证基础字段
            Assert.IsNotNull(noiseData.sensorId, "传感器ID不应为空");
            Assert.IsNotNull(noiseData.deviceId, "设备ID不应为空");
            Assert.AreEqual("noise_001", noiseData.sensorId, "传感器ID应该正确");
            Assert.AreEqual("device_001", noiseData.deviceId, "设备ID应该正确");
            
            // 验证噪声数据
            Assert.AreEqual(75.5f, noiseData.noiseLevel, 0.01f, "噪声等级应该正确");
            Assert.AreEqual("dB", noiseData.unit, "单位应该正确");
            Assert.IsTrue(noiseData.isThresholdExceeded, "阈值超标状态应该正确");
            Assert.AreEqual(70f, noiseData.threshold, 0.01f, "阈值应该正确");
            
            // 验证位置数据
            Assert.AreEqual(10f, noiseData.location.x, 0.01f, "X坐标应该正确");
            Assert.AreEqual(5f, noiseData.location.y, 0.01f, "Y坐标应该正确");
            Assert.AreEqual(15f, noiseData.location.z, 0.01f, "Z坐标应该正确");
            
            // 验证时间戳
            Assert.IsTrue(noiseData.timestamp != default(DateTime), "时间戳应该有效");
        }

        /// <summary>
        /// 测试噪声传感器数据JSON序列化
        /// </summary>
        [Test]
        public void TestNoiseSensorDataSerialization()
        {
            var originalData = new NoiseSensorData
            {
                timestamp = new DateTime(2024, 1, 1, 10, 0, 0, DateTimeKind.Utc),
                sensorId = "noise_test",
                deviceId = "device_test",
                noiseLevel = 65.3f,
                unit = "dB",
                isThresholdExceeded = false,
                threshold = 70f,
                location = new Vector3(100f, 50f, 25f)
            };
            
            // 序列化
            string json = JsonUtility.ToJson(originalData);
            Assert.IsNotNull(json, "JSON序列化不应为空");
            Assert.IsTrue(json.Contains("noise_test"), "JSON应包含传感器ID");
            Assert.IsTrue(json.Contains("65.3"), "JSON应包含噪声等级");
            
            // 反序列化
            var deserializedData = JsonUtility.FromJson<NoiseSensorData>(json);
            Assert.IsNotNull(deserializedData, "反序列化数据不应为空");
            Assert.AreEqual(originalData.sensorId, deserializedData.sensorId, "传感器ID应该一致");
            Assert.AreEqual(originalData.noiseLevel, deserializedData.noiseLevel, 0.01f, "噪声等级应该一致");
            Assert.AreEqual(originalData.unit, deserializedData.unit, "单位应该一致");
            Assert.AreEqual(originalData.isThresholdExceeded, deserializedData.isThresholdExceeded, "阈值状态应该一致");
        }

        /// <summary>
        /// 测试噪声传感器数据边界值
        /// </summary>
        [Test]
        public void TestNoiseSensorDataBoundaryValues()
        {
            // 测试最小值
            var minData = new NoiseSensorData
            {
                noiseLevel = 0f,
                threshold = 0f,
                location = Vector3.zero
            };
            Assert.AreEqual(0f, minData.noiseLevel, "最小噪声等级应该为0");
            Assert.AreEqual(Vector3.zero, minData.location, "最小位置应该为零向量");
            
            // 测试最大值
            var maxData = new NoiseSensorData
            {
                noiseLevel = 120f,
                threshold = 120f,
                location = new Vector3(float.MaxValue, float.MaxValue, float.MaxValue)
            };
            Assert.AreEqual(120f, maxData.noiseLevel, "最大噪声等级应该正确");
            
            // 测试负值处理
            var negativeData = new NoiseSensorData
            {
                noiseLevel = -10f,
                threshold = -5f
            };
            Assert.AreEqual(-10f, negativeData.noiseLevel, "负值应该被保留（由传感器逻辑处理）");
        }

        #endregion

        #region 扬尘传感器数据结构测试

        /// <summary>
        /// 测试扬尘传感器数据结构基础功能
        /// </summary>
        [Test]
        public void TestDustSensorDataStructure()
        {
            var dustData = new DustSensorData
            {
                timestamp = DateTime.UtcNow,
                sensorId = "dust_001",
                deviceId = "device_001",
                totalDustLevel = 150f,
                pm25Level = 45.2f,
                pm10Level = 78.1f,
                tspLevel = 200f,
                unit = "μg/m³",
                isPM25Exceeded = true,
                isPM10Exceeded = false,
                pm25Threshold = 75f,
                pm10Threshold = 150f,
                location = new Vector3(20f, 10f, 30f)
            };
            
            // 验证基础字段
            Assert.IsNotNull(dustData.sensorId, "传感器ID不应为空");
            Assert.IsNotNull(dustData.deviceId, "设备ID不应为空");
            Assert.AreEqual("dust_001", dustData.sensorId, "传感器ID应该正确");
            Assert.AreEqual("device_001", dustData.deviceId, "设备ID应该正确");
            
            // 验证扬尘数据
            Assert.AreEqual(150f, dustData.totalDustLevel, 0.01f, "总扬尘等级应该正确");
            Assert.AreEqual(45.2f, dustData.pm25Level, 0.01f, "PM2.5等级应该正确");
            Assert.AreEqual(78.1f, dustData.pm10Level, 0.01f, "PM10等级应该正确");
            Assert.AreEqual(200f, dustData.tspLevel, 0.01f, "TSP等级应该正确");
            Assert.AreEqual("μg/m³", dustData.unit, "单位应该正确");
            
            // 验证阈值状态
            Assert.IsTrue(dustData.isPM25Exceeded, "PM2.5超标状态应该正确");
            Assert.IsFalse(dustData.isPM10Exceeded, "PM10超标状态应该正确");
            Assert.AreEqual(75f, dustData.pm25Threshold, 0.01f, "PM2.5阈值应该正确");
            Assert.AreEqual(150f, dustData.pm10Threshold, 0.01f, "PM10阈值应该正确");
            
            // 验证位置数据
            Assert.AreEqual(20f, dustData.location.x, 0.01f, "X坐标应该正确");
            Assert.AreEqual(10f, dustData.location.y, 0.01f, "Y坐标应该正确");
            Assert.AreEqual(30f, dustData.location.z, 0.01f, "Z坐标应该正确");
        }

        /// <summary>
        /// 测试扬尘传感器数据JSON序列化
        /// </summary>
        [Test]
        public void TestDustSensorDataSerialization()
        {
            var originalData = new DustSensorData
            {
                timestamp = new DateTime(2024, 1, 1, 10, 0, 0, DateTimeKind.Utc),
                sensorId = "dust_test",
                deviceId = "device_test",
                totalDustLevel = 120f,
                pm25Level = 35.5f,
                pm10Level = 65.8f,
                tspLevel = 180f,
                unit = "μg/m³",
                isPM25Exceeded = false,
                isPM10Exceeded = true,
                pm25Threshold = 75f,
                pm10Threshold = 150f,
                location = new Vector3(200f, 100f, 50f)
            };
            
            // 序列化
            string json = JsonUtility.ToJson(originalData);
            Assert.IsNotNull(json, "JSON序列化不应为空");
            Assert.IsTrue(json.Contains("dust_test"), "JSON应包含传感器ID");
            Assert.IsTrue(json.Contains("35.5"), "JSON应包含PM2.5等级");
            Assert.IsTrue(json.Contains("65.8"), "JSON应包含PM10等级");
            
            // 反序列化
            var deserializedData = JsonUtility.FromJson<DustSensorData>(json);
            Assert.IsNotNull(deserializedData, "反序列化数据不应为空");
            Assert.AreEqual(originalData.sensorId, deserializedData.sensorId, "传感器ID应该一致");
            Assert.AreEqual(originalData.totalDustLevel, deserializedData.totalDustLevel, 0.01f, "总扬尘等级应该一致");
            Assert.AreEqual(originalData.pm25Level, deserializedData.pm25Level, 0.01f, "PM2.5等级应该一致");
            Assert.AreEqual(originalData.pm10Level, deserializedData.pm10Level, 0.01f, "PM10等级应该一致");
            Assert.AreEqual(originalData.unit, deserializedData.unit, "单位应该一致");
        }

        /// <summary>
        /// 测试扬尘传感器数据边界值
        /// </summary>
        [Test]
        public void TestDustSensorDataBoundaryValues()
        {
            // 测试最小值
            var minData = new DustSensorData
            {
                totalDustLevel = 0f,
                pm25Level = 0f,
                pm10Level = 0f,
                tspLevel = 0f,
                pm25Threshold = 0f,
                pm10Threshold = 0f,
                location = Vector3.zero
            };
            Assert.AreEqual(0f, minData.totalDustLevel, "最小总扬尘等级应该为0");
            Assert.AreEqual(0f, minData.pm25Level, "最小PM2.5等级应该为0");
            Assert.AreEqual(0f, minData.pm10Level, "最小PM10等级应该为0");
            
            // 测试最大值
            var maxData = new DustSensorData
            {
                totalDustLevel = 500f,
                pm25Level = 300f,
                pm10Level = 400f,
                tspLevel = 600f,
                pm25Threshold = 300f,
                pm10Threshold = 400f
            };
            Assert.AreEqual(500f, maxData.totalDustLevel, "最大总扬尘等级应该正确");
            Assert.AreEqual(300f, maxData.pm25Level, "最大PM2.5等级应该正确");
            
            // 测试负值处理
            var negativeData = new DustSensorData
            {
                totalDustLevel = -10f,
                pm25Level = -5f,
                pm10Level = -8f
            };
            Assert.AreEqual(-10f, negativeData.totalDustLevel, "负值应该被保留（由传感器逻辑处理）");
        }

        #endregion

        #region 数据结构比较和验证测试

        /// <summary>
        /// 测试传感器数据结构的完整性验证
        /// </summary>
        [Test]
        public void TestSensorDataIntegrityValidation()
        {
            // 测试噪声数据完整性
            var noiseData = new NoiseSensorData();
            Assert.IsNotNull(noiseData, "噪声数据结构应该可以实例化");
            
            // 测试扬尘数据完整性
            var dustData = new DustSensorData();
            Assert.IsNotNull(dustData, "扬尘数据结构应该可以实例化");
            
            // 验证默认值
            Assert.AreEqual(default(DateTime), noiseData.timestamp, "默认时间戳应该为默认值");
            Assert.AreEqual(0f, dustData.totalDustLevel, "默认扬尘等级应该为0");
            Assert.IsFalse(noiseData.isThresholdExceeded, "默认阈值状态应该为false");
        }

        /// <summary>
        /// 测试传感器数据的空值处理
        /// </summary>
        [Test]
        public void TestSensorDataNullHandling()
        {
            // 测试空字符串处理
            var noiseData = new NoiseSensorData
            {
                sensorId = null,
                deviceId = "",
                unit = null
            };
            
            Assert.IsNull(noiseData.sensorId, "空传感器ID应该保持为null");
            Assert.AreEqual("", noiseData.deviceId, "空设备ID应该保持为空字符串");
            Assert.IsNull(noiseData.unit, "空单位应该保持为null");
            
            // 测试扬尘数据空值处理
            var dustData = new DustSensorData
            {
                sensorId = null,
                unit = ""
            };
            
            Assert.IsNull(dustData.sensorId, "空传感器ID应该保持为null");
            Assert.AreEqual("", dustData.unit, "空单位应该保持为空字符串");
        }

        #endregion
    }
}
