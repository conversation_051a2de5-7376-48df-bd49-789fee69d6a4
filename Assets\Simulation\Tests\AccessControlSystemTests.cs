using System;
using System.Collections;
using System.Collections.Generic;
using System.Threading;
using NUnit.Framework;
using UnityEngine;
using UnityEngine.TestTools;
using Cysharp.Threading.Tasks;
using Simulation.Systems;
using Simulation.Data;
using Simulation.IoT;
using Simulation.Simulators;

namespace Simulation.Tests
{
    /// <summary>
    /// 门禁系统单元测试
    /// 测试AccessControlSystem的身份识别、门禁流程、统计功能等
    /// </summary>
    public class AccessControlSystemTests : TestBase
    {
        private AccessControlSystem accessControlSystem;
        private WorkerSimulator testWorker;
        private VehicleSimulator testVehicle;
        
        [SetUp]
        public override void SetUp()
        {
            base.SetUp();
            
            // 创建门禁系统
            accessControlSystem = CreateTestComponent<AccessControlSystem>();
            
            // 创建测试工人
            var workerGameObject = new GameObject("TestWorker");
            workerGameObject.tag = "Worker";
            testWorker = workerGameObject.AddComponent<WorkerSimulator>();
            
            // 创建测试车辆
            var vehicleGameObject = new GameObject("TestVehicle");
            vehicleGameObject.tag = "Vehicle";
            testVehicle = vehicleGameObject.AddComponent<VehicleSimulator>();
            
            // 添加碰撞体用于检测
            var workerCollider = workerGameObject.AddComponent<BoxCollider>();
            workerCollider.isTrigger = true;
            
            var vehicleCollider = vehicleGameObject.AddComponent<BoxCollider>();
            vehicleCollider.isTrigger = true;
        }
        
        [TearDown]
        public override void TearDown()
        {
            // 停止门禁系统
            if (accessControlSystem != null && accessControlSystem.IsRunning)
            {
                accessControlSystem.StopSimulation();
            }
            
            // 清理测试对象
            if (testWorker != null)
            {
                UnityEngine.Object.DestroyImmediate(testWorker.gameObject);
            }
            
            if (testVehicle != null)
            {
                UnityEngine.Object.DestroyImmediate(testVehicle.gameObject);
            }
            
            base.TearDown();
        }
        
        #region 基础功能测试
        
        /// <summary>
        /// 测试门禁系统基础配置
        /// </summary>
        [Test]
        public void TestAccessControlSystemBasicConfiguration()
        {
            // 验证初始状态
            Assert.IsFalse(accessControlSystem.IsRunning, "门禁系统初始应该未运行");
            Assert.AreEqual(AccessGateState.Idle, accessControlSystem.CurrentState, "初始状态应该是空闲");
            Assert.AreEqual("main_gate", accessControlSystem.GateId, "门禁ID应该正确");
            Assert.AreEqual(0, accessControlSystem.DailyEntryCount, "日进入计数初始应该为0");
            Assert.AreEqual(0, accessControlSystem.DailyExitCount, "日离开计数初始应该为0");
        }
        
        /// <summary>
        /// 测试门禁系统启动和停止
        /// </summary>
        [UnityTest]
        public IEnumerator TestAccessControlSystemStartStop()
        {
            // 启动门禁系统
            accessControlSystem.StartSimulation();
            yield return new WaitForSeconds(0.1f);
            
            Assert.IsTrue(accessControlSystem.IsRunning, "门禁系统应该正在运行");
            Assert.AreEqual(AccessGateState.Idle, accessControlSystem.CurrentState, "运行状态应该是空闲");
            
            // 停止门禁系统
            accessControlSystem.StopSimulation();
            yield return new WaitForSeconds(0.1f);
            
            Assert.IsFalse(accessControlSystem.IsRunning, "门禁系统应该已停止");
        }
        
        /// <summary>
        /// 测试门禁系统配置验证
        /// </summary>
        [Test]
        public void TestAccessControlSystemConfigurationValidation()
        {
            // 验证门禁系统配置
            Assert.IsTrue(accessControlSystem.ValidateConfiguration(), "门禁系统配置应该有效");
            
            // 验证门禁类型
            Assert.IsNotNull(accessControlSystem.GateId, "门禁ID不应为空");
            Assert.IsNotEmpty(accessControlSystem.GateId, "门禁ID不应为空字符串");
            Assert.IsNotNull(accessControlSystem.GateName, "门禁名称不应为空");
        }
        
        #endregion
        
        #region 身份识别测试
        
        /// <summary>
        /// 测试工人身份识别
        /// </summary>
        [UnityTest]
        public IEnumerator TestWorkerIdentification()
        {
            bool accessEventReceived = false;
            AccessEventData receivedAccessEvent = null;
            
            // 订阅IoT事件
            if (IoTSystem.Instance != null)
            {
                IoTSystem.Instance.OnEventReceived += (eventData) =>
                {
                    if (eventData.eventType == "access_control" && eventData.data is AccessEventData accessData)
                    {
                        accessEventReceived = true;
                        receivedAccessEvent = accessData;
                    }
                };
            }
            
            // 启动门禁系统
            accessControlSystem.StartSimulation();
            yield return new WaitForSeconds(0.1f);
            
            // 将工人移动到检测范围内
            testWorker.transform.position = accessControlSystem.transform.position + Vector3.forward * 1f;
            
            // 等待检测和处理
            yield return new WaitForSeconds(3f);
            
            // 验证访问事件
            if (IoTSystem.Instance != null)
            {
                Assert.IsTrue(accessEventReceived, "应该接收到访问事件");
                Assert.IsNotNull(receivedAccessEvent, "访问事件数据不应为空");
                Assert.AreEqual("access_control", receivedAccessEvent.eventType, "事件类型应该正确");
            }
        }
        
        /// <summary>
        /// 测试车辆身份识别
        /// </summary>
        [UnityTest]
        public IEnumerator TestVehicleIdentification()
        {
            bool accessEventReceived = false;
            
            // 订阅IoT事件
            if (IoTSystem.Instance != null)
            {
                IoTSystem.Instance.OnEventReceived += (eventData) =>
                {
                    if (eventData.eventType == "access_control")
                    {
                        accessEventReceived = true;
                    }
                };
            }
            
            // 启动门禁系统
            accessControlSystem.StartSimulation();
            yield return new WaitForSeconds(0.1f);
            
            // 将车辆移动到检测范围内
            testVehicle.transform.position = accessControlSystem.transform.position + Vector3.forward * 1f;
            
            // 等待检测和处理
            yield return new WaitForSeconds(3f);
            
            // 验证访问事件
            if (IoTSystem.Instance != null)
            {
                Assert.IsTrue(accessEventReceived, "应该接收到车辆访问事件");
            }
        }
        
        /// <summary>
        /// 测试访问类型识别
        /// </summary>
        [UnityTest]
        public IEnumerator TestAccessTypeIdentification()
        {
            string detectedAccessType = "";
            
            // 订阅IoT事件
            if (IoTSystem.Instance != null)
            {
                IoTSystem.Instance.OnEventReceived += (eventData) =>
                {
                    if (eventData.eventType == "access_control" && eventData.data is AccessEventData accessData)
                    {
                        detectedAccessType = accessData.accessType;
                    }
                };
            }
            
            // 启动门禁系统
            accessControlSystem.StartSimulation();
            yield return new WaitForSeconds(0.1f);
            
            // 将工人移动到检测范围内
            testWorker.transform.position = accessControlSystem.transform.position + Vector3.forward * 1f;
            
            // 等待检测和处理
            yield return new WaitForSeconds(3f);
            
            // 验证访问类型
            if (IoTSystem.Instance != null)
            {
                Assert.IsNotEmpty(detectedAccessType, "应该检测到访问类型");
                Assert.IsTrue(detectedAccessType == "entry" || detectedAccessType == "exit", 
                    "访问类型应该是entry或exit");
            }
        }
        
        #endregion
        
        #region 门禁流程测试
        
        /// <summary>
        /// 测试门禁处理流程
        /// </summary>
        [UnityTest]
        public IEnumerator TestAccessProcessingFlow()
        {
            // 启动门禁系统
            accessControlSystem.StartSimulation();
            yield return new WaitForSeconds(0.1f);
            
            // 验证初始状态
            Assert.AreEqual(AccessGateState.Idle, accessControlSystem.CurrentState, "初始状态应该是空闲");
            
            // 将工人移动到检测范围内
            testWorker.transform.position = accessControlSystem.transform.position + Vector3.forward * 1f;
            
            // 等待检测触发
            yield return new WaitForSeconds(0.5f);
            
            // 等待处理完成
            yield return new WaitForSeconds(3f);
            
            // 验证最终状态
            Assert.AreEqual(AccessGateState.Idle, accessControlSystem.CurrentState, "处理完成后应该回到空闲状态");
        }
        
        /// <summary>
        /// 测试门禁方向判断
        /// </summary>
        [UnityTest]
        public IEnumerator TestAccessDirectionDetection()
        {
            List<string> detectedDirections = new List<string>();
            
            // 订阅IoT事件
            if (IoTSystem.Instance != null)
            {
                IoTSystem.Instance.OnEventReceived += (eventData) =>
                {
                    if (eventData.eventType == "access_control" && eventData.data is AccessEventData accessData)
                    {
                        detectedDirections.Add(accessData.accessType);
                    }
                };
            }
            
            // 启动门禁系统
            accessControlSystem.StartSimulation();
            yield return new WaitForSeconds(0.1f);
            
            // 测试进入方向（门禁前方）
            testWorker.transform.position = accessControlSystem.transform.position + Vector3.forward * 1f;
            yield return new WaitForSeconds(3f);
            
            // 移动工人到门禁后方测试离开方向
            testWorker.transform.position = accessControlSystem.transform.position + Vector3.back * 1f;
            yield return new WaitForSeconds(3f);
            
            // 验证方向检测
            if (IoTSystem.Instance != null && detectedDirections.Count > 0)
            {
                Assert.Contains("entry", detectedDirections, "应该检测到进入方向");
            }
        }
        
        /// <summary>
        /// 测试重复访问检查
        /// </summary>
        [UnityTest]
        public IEnumerator TestDuplicateAccessCheck()
        {
            int accessEventCount = 0;
            
            // 订阅IoT事件
            if (IoTSystem.Instance != null)
            {
                IoTSystem.Instance.OnEventReceived += (eventData) =>
                {
                    if (eventData.eventType == "access_control")
                    {
                        accessEventCount++;
                    }
                };
            }
            
            // 启动门禁系统
            accessControlSystem.StartSimulation();
            yield return new WaitForSeconds(0.1f);
            
            // 第一次访问
            testWorker.transform.position = accessControlSystem.transform.position + Vector3.forward * 1f;
            yield return new WaitForSeconds(3f);
            
            // 立即第二次访问（应该被重复检查阻止）
            testWorker.transform.position = accessControlSystem.transform.position + Vector3.forward * 1.5f;
            yield return new WaitForSeconds(3f);
            
            // 验证重复访问检查
            if (IoTSystem.Instance != null)
            {
                Assert.LessOrEqual(accessEventCount, 1, "重复访问应该被阻止");
            }
        }
        
        #endregion
        
        #region 统计功能测试
        
        /// <summary>
        /// 测试日统计功能
        /// </summary>
        [UnityTest]
        public IEnumerator TestDailyStatistics()
        {
            // 启动门禁系统
            accessControlSystem.StartSimulation();
            yield return new WaitForSeconds(0.1f);
            
            // 验证初始统计
            Assert.AreEqual(0, accessControlSystem.DailyEntryCount, "初始进入计数应该为0");
            Assert.AreEqual(0, accessControlSystem.DailyExitCount, "初始离开计数应该为0");
            
            // 模拟进入访问
            testWorker.transform.position = accessControlSystem.transform.position + Vector3.forward * 1f;
            yield return new WaitForSeconds(3f);
            
            // 验证统计更新
            Assert.GreaterOrEqual(accessControlSystem.DailyEntryCount, 0, "进入计数应该被更新");
        }
        
        /// <summary>
        /// 测试访问统计准确性
        /// </summary>
        [UnityTest]
        public IEnumerator TestAccessStatisticsAccuracy()
        {
            int initialEntryCount = accessControlSystem.DailyEntryCount;
            int initialExitCount = accessControlSystem.DailyExitCount;
            
            // 启动门禁系统
            accessControlSystem.StartSimulation();
            yield return new WaitForSeconds(0.1f);
            
            // 模拟多次访问
            for (int i = 0; i < 3; i++)
            {
                // 创建新的测试对象避免重复检查
                var newWorker = new GameObject($"TestWorker_{i}");
                newWorker.tag = "Worker";
                newWorker.AddComponent<WorkerSimulator>();
                newWorker.AddComponent<BoxCollider>().isTrigger = true;
                
                // 移动到检测范围
                newWorker.transform.position = accessControlSystem.transform.position + Vector3.forward * (1f + i * 0.1f);
                
                yield return new WaitForSeconds(3f);
                
                // 清理
                UnityEngine.Object.DestroyImmediate(newWorker);
            }
            
            // 验证统计变化
            int finalEntryCount = accessControlSystem.DailyEntryCount;
            int finalExitCount = accessControlSystem.DailyExitCount;
            
            Assert.GreaterOrEqual(finalEntryCount, initialEntryCount, "进入计数应该增加");
        }
        
        #endregion
    }
}
