using System.Collections;
using System.Collections.Generic;
using System.Threading;
using UnityEngine;
using UnityEngine.TestTools;
using NUnit.Framework;
using Cysharp.Threading.Tasks;
using Simulation.IoT;
using Simulation.Data;
using Simulation.Sensors;

namespace Simulation.Tests
{
    /// <summary>
    /// IoT系统单元测试
    /// </summary>
    public class IoTSystemTests : TestBase
    {
        private IoTSystem iotSystem;
        private MockSensor mockSensor;

        [SetUp]
        public override void SetUp()
        {
            base.SetUp();

            // 获取IoT系统实例
            iotSystem = IoTSystem.Instance;
            Assert.IsNotNull(iotSystem, "IoT系统实例不应为空");
        }

        /// <summary>
        /// 测试IoT系统单例模式
        /// </summary>
        [Test]
        public void TestSingletonPattern()
        {
            ReCreateMockSensor();
            // 验证单例实例
            var instance1 = IoTSystem.Instance;
            var instance2 = IoTSystem.Instance;

            Assert.IsNotNull(instance1, "第一次获取实例不应为空");
            Assert.IsNotNull(instance2, "第二次获取实例不应为空");
            Assert.AreSame(instance1, instance2, "两次获取的实例应该是同一个对象");
        }

        /// <summary>
        /// 测试传感器注册功能
        /// </summary>
        [Test]
        public void TestSensorRegistration()
        {
            ReCreateMockSensor();
            bool registrationResult = iotSystem.RegisterSensor(mockSensor);

            Assert.IsTrue(registrationResult, "传感器注册应该成功");
            Assert.IsTrue(iotSystem.IsSensorRegistered(mockSensor.SensorId), "传感器应该已注册");
        }

        /// <summary>
        /// 测试重复注册传感器
        /// </summary>
        [Test]
        public void TestDuplicateSensorRegistration()
        {
            ReCreateMockSensor();
            // 第一次注册
            bool firstRegistration = iotSystem.RegisterSensor(mockSensor);
            Assert.IsTrue(firstRegistration, "第一次注册应该成功");

            // 第二次注册相同ID
            bool secondRegistration = iotSystem.RegisterSensor(mockSensor);
            Assert.IsFalse(secondRegistration, "重复注册应该失败");
        }

        /// <summary>
        /// 测试传感器注销功能
        /// </summary>
        [Test]
        public void TestSensorUnregistration()
        {
            ReCreateMockSensor();
            var id = mockSensor.SensorId;
            // 注册传感器
            iotSystem.RegisterSensor(mockSensor);
            Assert.IsTrue(iotSystem.IsSensorRegistered(id), "传感器应该已注册");

            // 注销传感器
            bool unregistrationResult = iotSystem.UnregisterSensor(mockSensor.SensorId);
            Assert.IsTrue(unregistrationResult, "传感器注销应该成功");
            Assert.IsFalse(iotSystem.IsSensorRegistered(mockSensor.SensorId), "传感器应该已注销");
        }

        /// <summary>
        /// 测试传感器数据收集
        /// </summary>
        [Test]
        public void TestSensorDataCollection()
        {
            string sensorId = "test_sensor_0005";
            var testData = new MockSensorData("mock");

            // 收集传感器数据
            iotSystem.CollectSensorData(sensorId, testData);

            // 验证数据格式
            Assert.IsNotNull(testData.timestamp, "时间戳不应为空");
            Assert.AreEqual("mock", testData.sensorType, "传感器类型应该匹配");
        }

        /// <summary>
        /// 测试IoT事件发送
        /// </summary>
        [Test]
        public void TestIoTEventSending()
        {
            string sourceId = "event_test_id001";
            string eventType = "test_event";
            string title = "测试事件";
            string description = "这是一个测试事件";
            var eventData = new { testValue = 123 };

            // 发送IoT事件
            iotSystem.SendIOTEvent(sourceId, eventType, title, description, eventData);

            // 这里主要测试方法不会抛出异常
            // 实际的事件处理验证需要在集成测试中进行
            Assert.Pass("IoT事件发送完成");
        }

        /// <summary>
        /// 测试获取注册传感器列表
        /// </summary>
        [Test]
        public void TestGetRegisteredSensors()
        {
            var sensor1 = testGameObject.AddComponent<MockSensor>();
            var sensor2 = testGameObject.AddComponent<MockSensor>();

            iotSystem.RegisterSensor(sensor1);
            iotSystem.RegisterSensor(sensor2);

            // 获取注册传感器列表
            var registeredSensors = iotSystem.GetRegisteredSensors();

            Assert.IsNotNull(registeredSensors, "注册传感器列表不应为空");
            Assert.Contains(sensor1.SensorId, registeredSensors, "应该包含传感器1");
            Assert.Contains(sensor2.SensorId, registeredSensors, "应该包含传感器2");
        }

        /// <summary>
        /// 测试空传感器ID处理
        /// </summary>
        [Test]
        public void TestNullSensorIdHandling()
        {
            // 测试空传感器ID注册
            mockSensor = null;
            bool registrationResult = iotSystem.RegisterSensor(mockSensor);
            Assert.IsFalse(registrationResult, "空传感器ID注册应该失败");

            // 测试空传感器ID注销
            bool unregistrationResult = iotSystem.UnregisterSensor(null);
            Assert.IsFalse(unregistrationResult, "空传感器ID注销应该失败");
        }

        /// <summary>
        /// 测试传感器数据JSON序列化
        /// </summary>
        [Test]
        public void TestSensorDataSerialization()
        {
            var testData = new MockSensorData("mock");
            testData.testValue = 42;
            testData.testString = "测试字符串";

            // 测试JSON序列化
            AssertJsonSerialization(testData);
        }

        void ReCreateMockSensor()
        {
            if (mockSensor != null)
            {
                if (mockSensor.IsRunning) mockSensor.StopSensor();
                Object.Destroy(mockSensor);
            }
            
            mockSensor = testGameObject.AddComponent<MockSensor>();
        }
    }

    /// <summary>
    /// 模拟传感器类，用于测试
    /// </summary>
    public class MockSensor : SensorBase
    {
        public override string SensorType => "mock";

        protected override async UniTask<object> GenerateSensorDataAsync(CancellationToken cancellationToken)
        {
            // 模拟数据收集
            await UniTask.Yield(PlayerLoopTiming.Update, cancellationToken);
            var data = new MockSensorData(SensorType);
            return data;
        }
    }

    /// <summary>
    /// 模拟传感器数据类，用于测试
    /// </summary>
    [System.Serializable]
    public class MockSensorData : SensorDataBase
    {
        public int testValue;
        public string testString;

        public MockSensorData(string sensorType)
            : base(sensorType)
        {
        }
    }
}
