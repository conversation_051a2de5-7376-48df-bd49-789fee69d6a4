# 快速参考卡片 - Unity建筑工地数字孪生模拟系统

## 🚀 快速开始

### 项目启动检查清单
- [ ] Unity 2022.3 LTS 已安装
- [ ] UniTask 插件已导入
- [ ] BestMQTT 插件已导入
- [ ] 阅读了 RuleAndGuidelines.md
- [ ] 运行了基础测试验证环境

### 核心组件快速访问
```csharp
// IoT系统
var iot = IoTSystem.Instance;

// 模拟管理器
var sim = SimulationManager.Instance;

// MQTT管理器
var mqtt = iot.GetComponent<MQTTManager>();
```

## 📝 常用代码模板

### 1. 创建新传感器
```csharp
public class MySensor : SensorBase
{
    public MySensor() : base("my_sensor_type")
    {
        sampleRate = 1f;
        autoStart = true;
        enableDebugLog = false;
    }
    
    protected override void CollectData()
    {
        var data = new MySensorData("my_sensor_type");
        OnDataGenerated?.Invoke(SensorId, data);
    }
}

[System.Serializable]
public class MySensorData : SensorDataBase
{
    public float value;
    
    public MySensorData(string sensorType) : base(sensorType)
    {
        value = UnityEngine.Random.Range(0f, 100f);
    }
}
```

### 2. 创建新模拟器
```csharp
public class MySimulator : SimulatorBase
{
    [Header("模拟器配置")]
    [SerializeField] private float simulationSpeed = 1f;
    
    public MySimulator() : base("my_simulator")
    {
        updateInterval = 1f;
    }
    
    protected override async UniTask SimulationLoop(CancellationToken cancellationToken)
    {
        while (!cancellationToken.IsCancellationRequested)
        {
            // 执行模拟逻辑
            await PerformSimulationStep();
            
            // 发送事件
            SendSimulationEvent("step_completed", new { step = currentStep });
            
            // 等待下一次更新
            await UniTask.Delay(TimeSpan.FromSeconds(updateInterval), cancellationToken: cancellationToken);
        }
    }
    
    private async UniTask PerformSimulationStep()
    {
        // 模拟业务逻辑
        await UniTask.Delay(100); // 模拟处理时间
    }
    
    private void SendSimulationEvent(string eventType, object eventData)
    {
        IoTSystem.Instance?.SendIOTEvent(simulatorId, eventType, "模拟器事件", "描述", eventData);
    }
}
```

### 3. 创建单元测试
```csharp
public class MyComponentTests : TestBase
{
    [UnityTest]
    public IEnumerator TestMyComponent()
    {
        // 创建测试组件
        var component = CreateTestComponent<MyComponent>();
        
        // 设置测试条件
        bool eventTriggered = false;
        component.OnEvent += () => eventTriggered = true;
        
        // 执行操作
        component.DoSomething();
        
        // 等待结果
        yield return WaitForCondition(() => eventTriggered, 5f, "事件触发超时");
        
        // 验证结果
        Assert.IsTrue(eventTriggered, "事件应该被触发");
    }
}
```

## 🔧 常用配置

### Inspector配置模板
```csharp
[Header("基础设置")]
[SerializeField] private string componentId = "default_id";
[SerializeField] private float updateRate = 1f;
[SerializeField] private bool autoStart = true;

[Header("调试选项")]
[SerializeField] private bool enableDebugLog = false;
[SerializeField] private bool showGizmos = true;

[Header("运行时状态 (只读)")]
[SerializeField, ReadOnly] private bool isRunning = false;
[SerializeField, ReadOnly] private int updateCount = 0;

[ContextMenu("启动")]
public void StartFromMenu() => StartAsync().Forget();

[ContextMenu("停止")]
public void StopFromMenu() => Stop();

[ContextMenu("重置")]
public void ResetFromMenu() => Reset();
```

### MQTT配置模板
```csharp
[Header("MQTT配置")]
[SerializeField] private string brokerAddress = "localhost";
[SerializeField] private int brokerPort = 1883;
[SerializeField] private string clientId = "unity_client";
[SerializeField] private bool useSSL = false;
[SerializeField] private bool autoReconnect = true;

private void ConfigureMQTT()
{
    var mqtt = GetComponent<MQTTManager>();
    mqtt.brokerAddress = brokerAddress;
    mqtt.brokerPort = brokerPort;
    mqtt.clientId = clientId;
    mqtt.useSSL = useSSL;
    mqtt.autoReconnect = autoReconnect;
}
```

## 🧪 测试快速指南

### 运行测试的方法
```csharp
// 1. Unity Test Runner
Window -> General -> Test Runner

// 2. 代码中运行特定测试
[ContextMenu("运行测试")]
public void RunTests()
{
    var testRunner = FindObjectOfType<IntegrationTestRunner>();
    testRunner?.RunAllIntegrationTests();
}

// 3. 验证测试覆盖率
[ContextMenu("检查覆盖率")]
public void CheckCoverage()
{
    var validator = FindObjectOfType<TestCoverageValidator>();
    validator?.ValidateTestCoverage();
}
```

### 测试环境设置
```csharp
[SetUp]
public override void SetUp()
{
    base.SetUp();
    SimulationManager.IsInTestEnvironment = true;
    // 其他测试设置
}

[TearDown]
public override void TearDown()
{
    SimulationManager.IsInTestEnvironment = false;
    base.TearDown();
}
```

## 🚨 故障排除速查

### 编译错误
| 错误类型 | 解决方案 |
|---------|---------|
| 命名空间冲突 | 使用 `Simulation.IoTSystem` |
| 重复定义 | 使用 `TestDataTypes.cs` 中的统一定义 |
| 缺少引用 | 添加 `using Simulation.IoTSystem;` |
| 异步方法错误 | 确保使用 `UniTask` 而非 `Task` |

### 运行时问题
| 问题 | 检查项 |
|------|-------|
| MQTT连接失败 | 网络、地址、端口、认证 |
| 传感器无数据 | 启动状态、采样率、事件订阅 |
| 模拟器不运行 | 启动方法、异常捕获、取消令牌 |
| 测试失败 | 超时设置、环境隔离、资源清理 |

### 性能问题
| 症状 | 可能原因 | 解决方案 |
|------|---------|---------|
| 帧率下降 | 更新频率过高 | 调整 `updateInterval` |
| 内存泄漏 | 未取消订阅 | 检查事件取消订阅 |
| GC频繁 | 频繁分配对象 | 使用对象池 |
| 异步阻塞 | 同步等待异步操作 | 使用正确的异步模式 |

## 📚 重要文档链接

- **项目规则**: `Assets/Simulation/Docs/RuleAndGuidelines.md`
- **需求文档**: `Assets/Simulation/Docs/需求文档.md`
- **开发指导**: `Assets/Simulation/Docs/开发指导.md`
- **业务逻辑**: `Assets/Simulation/Docs/项目业务逻辑.md`
- **测试文档**: `Assets/Simulation/Tests/README.md`
- **UniTask文档**: `TechDocs/UniTaskDoc.md`
- **MQTT文档**: `TechDocs/BestDocs/MQTT文档`

## 🎯 开发检查清单

### 每次提交前检查
- [ ] 代码编译无错误无警告
- [ ] 相关测试通过
- [ ] 遵循命名规范
- [ ] 添加了必要的注释
- [ ] 更新了相关文档

### 新功能开发检查
- [ ] 设计符合项目架构原则
- [ ] 实现了对应的测试
- [ ] 添加了Inspector配置
- [ ] 提供了Context Menu调试功能
- [ ] 文档已更新

### 性能优化检查
- [ ] 异步操作正确实现
- [ ] 没有内存泄漏
- [ ] 更新频率合理
- [ ] 资源正确释放

---

**记住**: 遇到问题时，先查看这个快速参考，然后查阅详细的 RuleAndGuidelines.md 文档！
