%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: e8e379c3caac49a47bb3f403075b7be2, type: 3}
  m_Name: MQTTConnectionPreset
  m_EditorClassIdentifier: 
  presetName: "\u9ED8\u8BA4\u914D\u7F6E"
  description: "MQTT\u8FDE\u63A5\u914D\u7F6E\u63CF\u8FF0"
  brokerAddress: localhost
  brokerPort: 1883
  clientId: simulation_client
  username: 
  password: 
  useSSL: 0
  keepAliveInterval: 60
  connectionTimeout: 30
  transport: 0
  websocketPath: /mqtt
  autoReconnect: 1
  reconnectDelay: 5
  maxReconnectAttempts: 10
  topicPrefix: simulation
  qosLevel: 1
  retainMessages: 1
